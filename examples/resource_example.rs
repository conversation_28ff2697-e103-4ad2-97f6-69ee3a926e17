// This would normally be imported from the crate
// use verturion::core::classes::resources::{Resource, ResourceLoader};

// For now, we'll include a simplified version for demonstration
use std::collections::HashMap;
use std::sync::{Arc, RwLock};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct Resource {
    pub resource_path: String,
    pub resource_name: String,
    pub resource_local_to_scene: bool,
    pub instance_id: u64,
    pub is_dirty: bool,
    pub metadata: HashMap<String, String>,
}

impl Resource {
    pub fn new() -> Self {
        static mut NEXT_ID: u64 = 1;
        let instance_id = unsafe {
            let id = NEXT_ID;
            NEXT_ID += 1;
            id
        };
        
        Self {
            resource_path: String::new(),
            resource_name: String::new(),
            resource_local_to_scene: false,
            instance_id,
            is_dirty: false,
            metadata: HashMap::new(),
        }
    }

    pub fn set_path(&mut self, path: String) {
        self.resource_path = path;
        self.is_dirty = true;
    }

    pub fn set_name(&mut self, name: String) {
        self.resource_name = name;
        self.is_dirty = true;
    }

    pub fn set_metadata(&mut self, key: String, value: String) {
        self.metadata.insert(key, value);
        self.is_dirty = true;
    }

    pub fn get_metadata(&self, key: &str) -> Option<&String> {
        self.metadata.get(key)
    }

    pub fn duplicate(&self) -> Self {
        let mut duplicate = Self::new();
        duplicate.resource_name = self.resource_name.clone();
        duplicate.resource_local_to_scene = self.resource_local_to_scene;
        duplicate.metadata = self.metadata.clone();
        duplicate
    }
}

pub struct ResourceLoader {
    cache: RwLock<HashMap<String, Arc<Resource>>>,
}

impl ResourceLoader {
    pub fn new() -> Self {
        Self {
            cache: RwLock::new(HashMap::new()),
        }
    }

    pub fn load(&self, path: &str) -> Result<Arc<Resource>, String> {
        // Check if the resource is already cached
        {
            let cache = self.cache.read().unwrap();
            if let Some(resource) = cache.get(path) {
                return Ok(resource.clone());
            }
        }

        // Create a new resource (in a real implementation, this would load from disk)
        let mut resource = Resource::new();
        resource.set_path(path.to_string());
        resource.set_name(
            std::path::Path::new(path)
                .file_stem()
                .and_then(|s| s.to_str())
                .unwrap_or("Unknown")
                .to_string()
        );

        let arc_resource = Arc::new(resource);

        // Cache the resource
        {
            let mut cache = self.cache.write().unwrap();
            cache.insert(path.to_string(), arc_resource.clone());
        }

        Ok(arc_resource)
    }

    pub fn has_cached(&self, path: &str) -> bool {
        let cache = self.cache.read().unwrap();
        cache.contains_key(path)
    }

    pub fn get_cached_count(&self) -> usize {
        let cache = self.cache.read().unwrap();
        cache.len()
    }
}

fn main() {
    println!("Verturion Resource System Example");
    println!("==================================");

    // Create a resource loader
    let loader = ResourceLoader::new();

    // Create some resources
    let mut texture_resource = Resource::new();
    texture_resource.set_name("PlayerTexture".to_string());
    texture_resource.set_path("res://textures/player.png".to_string());
    texture_resource.set_metadata("format".to_string(), "PNG".to_string());
    texture_resource.set_metadata("size".to_string(), "64x64".to_string());

    let mut audio_resource = Resource::new();
    audio_resource.set_name("JumpSound".to_string());
    audio_resource.set_path("res://audio/jump.ogg".to_string());
    audio_resource.set_metadata("format".to_string(), "OGG".to_string());
    audio_resource.set_metadata("duration".to_string(), "0.5s".to_string());

    println!("Created Resources:");
    println!("- Texture: {} (ID: {})", texture_resource.resource_name, texture_resource.instance_id);
    println!("  Path: {}", texture_resource.resource_path);
    println!("  Format: {}", texture_resource.get_metadata("format").unwrap_or(&"Unknown".to_string()));
    println!("  Size: {}", texture_resource.get_metadata("size").unwrap_or(&"Unknown".to_string()));
    println!("  Dirty: {}", texture_resource.is_dirty);

    println!();
    println!("- Audio: {} (ID: {})", audio_resource.resource_name, audio_resource.instance_id);
    println!("  Path: {}", audio_resource.resource_path);
    println!("  Format: {}", audio_resource.get_metadata("format").unwrap_or(&"Unknown".to_string()));
    println!("  Duration: {}", audio_resource.get_metadata("duration").unwrap_or(&"Unknown".to_string()));
    println!("  Dirty: {}", audio_resource.is_dirty);

    println!();
    println!("Resource Duplication:");
    let texture_copy = texture_resource.duplicate();
    println!("- Original ID: {}, Copy ID: {}", texture_resource.instance_id, texture_copy.instance_id);
    println!("- Names match: {}", texture_resource.resource_name == texture_copy.resource_name);

    println!();
    println!("Resource Loading and Caching:");
    
    // Load resources through the loader
    let loaded1 = loader.load("res://scenes/main.tscn").unwrap();
    let loaded2 = loader.load("res://scenes/player.tscn").unwrap();
    let loaded3 = loader.load("res://scenes/main.tscn").unwrap(); // Should be cached

    println!("- Loaded main.tscn: {}", loaded1.resource_name);
    println!("- Loaded player.tscn: {}", loaded2.resource_name);
    println!("- Loaded main.tscn again (cached): {}", loaded3.resource_name);
    println!("- Same instance? {}", Arc::ptr_eq(&loaded1, &loaded3));
    println!("- Cache count: {}", loader.get_cached_count());
    println!("- main.tscn is cached: {}", loader.has_cached("res://scenes/main.tscn"));
    println!("- nonexistent.tscn is cached: {}", loader.has_cached("res://scenes/nonexistent.tscn"));

    println!();
    println!("This demonstrates the Resource system features:");
    println!("- Resource creation with metadata");
    println!("- Resource duplication with unique IDs");
    println!("- Resource loading and caching");
    println!("- Memory-efficient resource sharing");
    println!("- Path-based resource identification");
}
