// This would normally be imported from the crate
// use verturion::core::classes::variants::{Dictionary, Variant};

// For now, we'll include a simplified version for demonstration
use std::collections::HashMap;
use std::fmt;

/// Simplified Variant enum for demonstration
#[derive(Debug, Clone)]
pub enum Variant {
    Nil,
    Bool(bool),
    Int(i32),
    Float(f32),
    String(String),
}

impl Variant {
    pub fn new_nil() -> Self {
        Self::Nil
    }
    
    pub fn new_bool(value: bool) -> Self {
        Self::Bool(value)
    }
    
    pub fn new_int(value: i32) -> Self {
        Self::Int(value)
    }
    
    pub fn new_float(value: f32) -> Self {
        Self::Float(value)
    }
    
    pub fn new_string(value: String) -> Self {
        Self::String(value)
    }
}

impl fmt::Display for Variant {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            Variant::Nil => write!(f, "null"),
            Variant::Bool(b) => write!(f, "{}", b),
            Variant::Int(i) => write!(f, "{}", i),
            Variant::Float(fl) => write!(f, "{}", fl),
            Variant::String(s) => write!(f, "\"{}\"", s),
        }
    }
}

impl PartialEq for Variant {
    fn eq(&self, other: &Self) -> bool {
        match (self, other) {
            (Variant::Nil, Variant::Nil) => true,
            (Variant::Bool(a), Variant::Bool(b)) => a == b,
            (Variant::Int(a), Variant::Int(b)) => a == b,
            (Variant::Float(a), Variant::Float(b)) => (a - b).abs() < f32::EPSILON,
            (Variant::String(a), Variant::String(b)) => a == b,
            _ => false,
        }
    }
}

/// Simplified Dictionary implementation for demonstration
pub struct Dictionary {
    data: HashMap<String, Variant>,
    read_only: bool,
}

impl Dictionary {
    pub fn new() -> Self {
        Self {
            data: HashMap::new(),
            read_only: false,
        }
    }
    
    pub fn from_pairs(pairs: Vec<(String, Variant)>) -> Self {
        let mut dict = Dictionary::new();
        for (key, value) in pairs {
            dict.set(key, value).unwrap();
        }
        dict
    }
    
    pub fn size(&self) -> usize {
        self.data.len()
    }
    
    pub fn is_empty(&self) -> bool {
        self.data.is_empty()
    }
    
    pub fn is_read_only(&self) -> bool {
        self.read_only
    }
    
    pub fn make_read_only(&mut self) {
        self.read_only = true;
    }
    
    pub fn set(&mut self, key: String, value: Variant) -> Result<bool, String> {
        if self.read_only {
            return Err("Dictionary is read-only".to_string());
        }
        let existed = self.data.contains_key(&key);
        self.data.insert(key, value);
        Ok(existed)
    }
    
    pub fn get(&self, key: &str) -> Option<&Variant> {
        self.data.get(key)
    }
    
    pub fn get_or_default(&self, key: &str, default: Variant) -> Variant {
        if let Some(value) = self.get(key) {
            value.clone()
        } else {
            default
        }
    }
    
    pub fn has(&self, key: &str) -> bool {
        self.data.contains_key(key)
    }
    
    pub fn has_all(&self, keys: &[&str]) -> bool {
        keys.iter().all(|key| self.has(key))
    }
    
    pub fn erase(&mut self, key: &str) -> Result<bool, String> {
        if self.read_only {
            return Err("Dictionary is read-only".to_string());
        }
        Ok(self.data.remove(key).is_some())
    }
    
    pub fn find_key(&self, value: &Variant) -> Option<String> {
        for (key, val) in &self.data {
            if val == value {
                return Some(key.clone());
            }
        }
        None
    }
    
    pub fn keys(&self) -> Vec<String> {
        self.data.keys().cloned().collect()
    }
    
    pub fn values(&self) -> Vec<Variant> {
        self.data.values().cloned().collect()
    }
    
    pub fn clear(&mut self) -> Result<(), String> {
        if self.read_only {
            return Err("Dictionary is read-only".to_string());
        }
        self.data.clear();
        Ok(())
    }
    
    pub fn duplicate(&self) -> Dictionary {
        Dictionary {
            data: self.data.clone(),
            read_only: false,
        }
    }
    
    pub fn merge(&mut self, other: &Dictionary, overwrite: bool) -> Result<(), String> {
        if self.read_only {
            return Err("Dictionary is read-only".to_string());
        }
        
        for (key, value) in &other.data {
            if overwrite || !self.data.contains_key(key) {
                self.data.insert(key.clone(), value.clone());
            }
        }
        
        Ok(())
    }
    
    pub fn merged(&self, other: &Dictionary, overwrite: bool) -> Dictionary {
        let mut result = self.duplicate();
        let _ = result.merge(other, overwrite);
        result
    }
    
    pub fn hash(&self) -> u32 {
        // Simple hash based on size for demonstration
        self.data.len() as u32
    }
    
    pub fn to_hashmap(&self) -> HashMap<String, Variant> {
        self.data.clone()
    }
}

impl fmt::Display for Dictionary {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{{")?;
        let mut first = true;
        for (key, value) in &self.data {
            if !first {
                write!(f, ", ")?;
            }
            write!(f, "\"{}\": {}", key, value)?;
            first = false;
        }
        write!(f, "}}")
    }
}

fn main() {
    println!("Verturion Dictionary System Example");
    println!("===================================");

    // Create a new dictionary
    let mut dict = Dictionary::new();
    
    println!("Created empty dictionary: {}", dict);
    println!("Size: {}, Empty: {}", dict.size(), dict.is_empty());

    // Add some key-value pairs
    dict.set("name".to_string(), Variant::new_string("Player".to_string())).unwrap();
    dict.set("level".to_string(), Variant::new_int(42)).unwrap();
    dict.set("health".to_string(), Variant::new_float(100.0)).unwrap();
    dict.set("alive".to_string(), Variant::new_bool(true)).unwrap();
    dict.set("inventory".to_string(), Variant::new_nil()).unwrap();

    println!("\nAfter adding elements: {}", dict);
    println!("Size: {}, Empty: {}", dict.size(), dict.is_empty());

    // Access elements
    println!("\nAccessing elements:");
    for key in dict.keys() {
        if let Some(value) = dict.get(&key) {
            println!("  [\"{}\"] = {}", key, value);
        }
    }

    // Modify an element
    dict.set("level".to_string(), Variant::new_int(43)).unwrap();
    println!("\nAfter modifying level: {}", dict);

    // Search operations
    println!("\nSearch operations:");
    println!("Has 'name': {}", dict.has("name"));
    println!("Has 'missing': {}", dict.has("missing"));
    println!("Has all ['name', 'level']: {}", dict.has_all(&["name", "level"]));
    println!("Has all ['name', 'missing']: {}", dict.has_all(&["name", "missing"]));
    
    if let Some(key) = dict.find_key(&Variant::new_bool(true)) {
        println!("Found key for value 'true': {}", key);
    }

    // Get with default
    let default_score = dict.get_or_default("score", Variant::new_int(0));
    println!("Score (with default): {}", default_score);

    // Dictionary manipulation
    println!("\nDictionary manipulation:");
    
    // Duplicate the dictionary
    let duplicated = dict.duplicate();
    println!("Duplicated dictionary: {}", duplicated);
    
    // Create another dictionary to merge
    let other_dict = Dictionary::from_pairs(vec![
        ("score".to_string(), Variant::new_int(1000)),
        ("level".to_string(), Variant::new_int(50)), // This will test overwrite behavior
        ("weapon".to_string(), Variant::new_string("sword".to_string())),
    ]);
    
    println!("Other dictionary: {}", other_dict);
    
    // Merge without overwrite
    let mut merged_no_overwrite = dict.duplicate();
    merged_no_overwrite.merge(&other_dict, false).unwrap();
    println!("Merged (no overwrite): {}", merged_no_overwrite);
    
    // Merge with overwrite
    let merged_with_overwrite = dict.merged(&other_dict, true);
    println!("Merged (with overwrite): {}", merged_with_overwrite);
    
    // Remove an element
    dict.erase("inventory").unwrap();
    println!("After removing 'inventory': {}", dict);
    
    // Hash value
    println!("Dictionary hash: {}", dict.hash());

    // Read-only operations
    println!("\nRead-only operations:");
    let mut readonly_dict = Dictionary::from_pairs(vec![
        ("readonly".to_string(), Variant::new_string("value".to_string())),
        ("immutable".to_string(), Variant::new_bool(true)),
    ]);
    
    println!("Before making read-only: {}", readonly_dict);
    readonly_dict.make_read_only();
    println!("Is read-only: {}", readonly_dict.is_read_only());
    
    // Try to modify (should fail)
    match readonly_dict.set("new_key".to_string(), Variant::new_string("fail".to_string())) {
        Ok(_) => println!("Successfully added to read-only dictionary (unexpected!)"),
        Err(e) => println!("Failed to add to read-only dictionary: {}", e),
    }

    // Clear dictionary
    dict.clear().unwrap();
    println!("\nAfter clearing: {}", dict);
    println!("Size: {}, Empty: {}", dict.size(), dict.is_empty());

    println!("\nThis demonstrates the Dictionary system features:");
    println!("- Dynamic key-value storage with type-safe operations");
    println!("- Search and manipulation methods");
    println!("- Read-only protection");
    println!("- Merging and duplication");
    println!("- Key-based access and modification");
    println!("- Hash computation and comparison");
}
