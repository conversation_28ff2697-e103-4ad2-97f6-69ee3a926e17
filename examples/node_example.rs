use std::rc::Rc;
use std::cell::RefCell;

// This would normally be imported from the crate
// use verturion::core::classes::nodes::Node;

// For now, we'll include a simplified version for demonstration
use std::collections::HashMap;

#[derive(Debug)]
pub struct Node {
    pub name: String,
    children: Vec<Rc<RefCell<Node>>>,
}

impl Node {
    pub fn new(name: String) -> Self {
        Self {
            name,
            children: Vec::new(),
        }
    }

    pub fn add_child(&mut self, child: Rc<RefCell<Node>>) {
        self.children.push(child);
    }

    pub fn get_child_count(&self) -> usize {
        self.children.len()
    }

    pub fn print_tree(&self) {
        self.print_tree_recursive(0);
    }

    fn print_tree_recursive(&self, depth: usize) {
        let indent = "  ".repeat(depth);
        println!("{}{}", indent, self.name);
        for child in &self.children {
            child.borrow().print_tree_recursive(depth + 1);
        }
    }
}

fn main() {
    println!("Verturion Node Example");
    println!("======================");

    // Create a scene tree
    let mut root = Node::new("Main".to_string());
    
    let player = Rc::new(RefCell::new(Node::new("Player".to_string())));
    let ui = Rc::new(RefCell::new(Node::new("UI".to_string())));
    let world = Rc::new(RefCell::new(Node::new("World".to_string())));

    // Add some children to player
    let sprite = Rc::new(RefCell::new(Node::new("Sprite2D".to_string())));
    let collision = Rc::new(RefCell::new(Node::new("CollisionShape2D".to_string())));
    player.borrow_mut().add_child(sprite);
    player.borrow_mut().add_child(collision);

    // Add some children to UI
    let health_bar = Rc::new(RefCell::new(Node::new("HealthBar".to_string())));
    let score_label = Rc::new(RefCell::new(Node::new("ScoreLabel".to_string())));
    ui.borrow_mut().add_child(health_bar);
    ui.borrow_mut().add_child(score_label);

    // Add main nodes to root
    root.add_child(player.clone());
    root.add_child(ui.clone());
    root.add_child(world.clone());

    // Print the scene tree
    println!("Scene Tree Structure:");
    root.print_tree();

    println!("\nNode Information:");
    println!("Root has {} children", root.get_child_count());
    println!("Player has {} children", player.borrow().get_child_count());
    println!("UI has {} children", ui.borrow().get_child_count());
    println!("World has {} children", world.borrow().get_child_count());

    println!("\nThis demonstrates the basic Node functionality:");
    println!("- Creating nodes with names");
    println!("- Building a hierarchical scene tree");
    println!("- Adding child nodes");
    println!("- Traversing and printing the tree structure");
    println!("- Querying node information");
}
