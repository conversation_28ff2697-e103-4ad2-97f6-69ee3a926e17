#!/usr/bin/env python3
"""
<PERSON><PERSON>t to analyze Godot 4.4 class documentation and compare with current Rust bindings.
"""

import re
import requests
from bs4 import BeautifulSoup
import os
from pathlib import Path

def extract_classes_from_docs():
    """Extract all class names from Godot documentation."""
    url = "https://docs.godotengine.org/en/stable/classes/index.html"
    response = requests.get(url)
    soup = BeautifulSoup(response.content, 'html.parser')
    
    classes = {
        'nodes': [],
        'resources': [],
        'other_objects': [],
        'editor_only': [],
        'variant_types': [],
        'globals': []
    }
    
    # Find all sections
    current_section = None
    
    # Look for section headers and class links
    for element in soup.find_all(['h1', 'h2', 'li']):
        if element.name in ['h1', 'h2']:
            text = element.get_text().strip().lower()
            if 'nodes' in text:
                current_section = 'nodes'
            elif 'resources' in text:
                current_section = 'resources'
            elif 'other objects' in text:
                current_section = 'other_objects'
            elif 'editor-only' in text:
                current_section = 'editor_only'
            elif 'variant types' in text:
                current_section = 'variant_types'
            elif 'globals' in text:
                current_section = 'globals'
        
        elif element.name == 'li' and current_section:
            # Look for class links
            link = element.find('a')
            if link and link.get('href', '').startswith('class_'):
                class_name = link.get_text().strip()
                if class_name and current_section:
                    classes[current_section].append(class_name)
    
    return classes

def extract_current_rust_classes():
    """Extract class names from current Rust module files."""
    base_path = Path("src/core/classes")
    
    rust_classes = {
        'nodes': [],
        'resources': [],
        'other_objects': [],
        'editor_only': [],
        'variant_types': [],
        'globals': []
    }
    
    for category in rust_classes.keys():
        mod_file = base_path / category / "mod.rs"
        if mod_file.exists():
            with open(mod_file, 'r') as f:
                content = f.read()
                # Extract module declarations
                matches = re.findall(r'pub mod ([^;]+);', content)
                rust_classes[category] = matches
    
    return rust_classes

def compare_classes(godot_classes, rust_classes):
    """Compare Godot classes with current Rust implementation."""
    comparison = {}
    
    for category in godot_classes.keys():
        godot_set = set(godot_classes[category])
        rust_set = set(rust_classes[category])
        
        comparison[category] = {
            'godot_total': len(godot_set),
            'rust_total': len(rust_set),
            'missing_in_rust': sorted(godot_set - rust_set),
            'extra_in_rust': sorted(rust_set - godot_set),
            'common': sorted(godot_set & rust_set)
        }
    
    return comparison

def main():
    print("Extracting classes from Godot documentation...")
    godot_classes = extract_classes_from_docs()
    
    print("Extracting classes from current Rust implementation...")
    rust_classes = extract_current_rust_classes()
    
    print("Comparing classes...")
    comparison = compare_classes(godot_classes, rust_classes)
    
    # Print results
    for category, data in comparison.items():
        print(f"\n=== {category.upper()} ===")
        print(f"Godot classes: {data['godot_total']}")
        print(f"Rust classes: {data['rust_total']}")
        print(f"Missing in Rust: {len(data['missing_in_rust'])}")
        print(f"Extra in Rust: {len(data['extra_in_rust'])}")
        
        if data['missing_in_rust']:
            print(f"\nMissing classes in Rust ({category}):")
            for cls in data['missing_in_rust'][:10]:  # Show first 10
                print(f"  - {cls}")
            if len(data['missing_in_rust']) > 10:
                print(f"  ... and {len(data['missing_in_rust']) - 10} more")
        
        if data['extra_in_rust']:
            print(f"\nExtra classes in Rust ({category}):")
            for cls in data['extra_in_rust'][:10]:  # Show first 10
                print(f"  - {cls}")
            if len(data['extra_in_rust']) > 10:
                print(f"  ... and {len(data['extra_in_rust']) - 10} more")

if __name__ == "__main__":
    main()
