use std::collections::HashMap;

/// Base class for all Godot-specific resource types.
///
/// Resource is the base class for all Godot-specific resource types, serving primarily as data containers.
/// Since they inherit from RefCounted, resources are reference-counted and freed when no longer in use.
/// They can also be nested within other resources, and saved on disk.
///
/// # Examples
///
/// ```
/// use verturion::core::classes::resources::Resource;
///
/// let mut resource = Resource::new();
/// resource.set_name("MyResource".to_string());
/// resource.set_path("res://my_resource.tres".to_string());
/// ```
#[derive(Debug, Clone)]
pub struct Resource {
    /// The unique path to this resource. If it has been saved to disk, the value will be its filepath.
    resource_path: String,

    /// An optional name for this resource. When defined, its value is displayed to represent the resource in the Inspector dock.
    resource_name: String,

    /// If true, the resource is duplicated for each instance of all scenes using it.
    resource_local_to_scene: bool,

    /// An unique identifier relative to this resource's scene.
    resource_scene_unique_id: String,

    /// The resource's unique instance ID.
    instance_id: u64,

    /// Whether the resource has been modified since last save.
    is_dirty: bool,

    /// Custom metadata associated with the resource.
    metadata: HashMap<String, String>,

    /// Reference to the local scene if this resource is local to scene.
    /// In a real implementation, this would be a proper Node reference.
    local_scene_id: Option<u64>,
}

/// Error types for resource operations.
#[derive(Debug, Clone, PartialEq)]
pub enum ResourceError {
    /// The resource file was not found.
    FileNotFound(String),
    /// The resource format is invalid or corrupted.
    InvalidFormat(String),
    /// Permission denied when accessing the resource.
    PermissionDenied(String),
    /// The resource is already loaded.
    AlreadyLoaded(String),
    /// Generic I/O error.
    IoError(String),
}

impl std::fmt::Display for ResourceError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ResourceError::FileNotFound(path) => write!(f, "Resource file not found: {}", path),
            ResourceError::InvalidFormat(msg) => write!(f, "Invalid resource format: {}", msg),
            ResourceError::PermissionDenied(path) => write!(f, "Permission denied: {}", path),
            ResourceError::AlreadyLoaded(path) => write!(f, "Resource already loaded: {}", path),
            ResourceError::IoError(msg) => write!(f, "I/O error: {}", msg),
        }
    }
}

impl std::error::Error for ResourceError {}

/// Result type for resource operations.
pub type ResourceResult<T> = Result<T, ResourceError>;

impl Resource {
    /// Creates a new Resource.
    pub fn new() -> Self {
        static mut NEXT_ID: u64 = 1;
        let instance_id = unsafe {
            let id = NEXT_ID;
            NEXT_ID += 1;
            id
        };

        Self {
            resource_path: String::new(),
            resource_name: String::new(),
            resource_local_to_scene: false,
            resource_scene_unique_id: String::new(),
            instance_id,
            is_dirty: false,
            metadata: HashMap::new(),
            local_scene_id: None,
        }
    }

    /// Returns the resource's unique instance ID.
    pub fn get_instance_id(&self) -> u64 {
        self.instance_id
    }

    /// Returns the unique path to this resource.
    pub fn get_path(&self) -> &str {
        &self.resource_path
    }

    /// Sets the unique path to this resource.
    pub fn set_path(&mut self, path: String) {
        self.resource_path = path;
        self.mark_dirty();
    }

    /// Returns the name of this resource.
    pub fn get_name(&self) -> &str {
        &self.resource_name
    }

    /// Sets the name of this resource.
    pub fn set_name(&mut self, name: String) {
        self.resource_name = name;
        self.mark_dirty();
    }

    /// Returns whether this resource is local to scene.
    pub fn is_local_to_scene(&self) -> bool {
        self.resource_local_to_scene
    }

    /// Sets whether this resource is local to scene.
    pub fn set_local_to_scene(&mut self, local_to_scene: bool) {
        self.resource_local_to_scene = local_to_scene;
        self.mark_dirty();
    }

    /// Returns the scene unique ID of this resource.
    pub fn get_scene_unique_id(&self) -> &str {
        &self.resource_scene_unique_id
    }

    /// Sets the scene unique ID of this resource.
    pub fn set_scene_unique_id(&mut self, id: String) {
        // Validate that the ID only contains letters, numbers, and underscores
        if id.chars().all(|c| c.is_alphanumeric() || c == '_') {
            self.resource_scene_unique_id = id;
        } else {
            // Generate a random ID if the provided one is invalid
            self.resource_scene_unique_id = Self::generate_scene_unique_id();
        }
        self.mark_dirty();
    }

    /// Returns whether the resource has been modified since last save.
    pub fn is_dirty(&self) -> bool {
        self.is_dirty
    }

    /// Marks the resource as dirty (modified).
    pub fn mark_dirty(&mut self) {
        self.is_dirty = true;
    }

    /// Marks the resource as clean (saved).
    pub fn mark_clean(&mut self) {
        self.is_dirty = false;
    }

    /// Returns whether this resource is built-in (from the engine).
    pub fn is_built_in(&self) -> bool {
        self.resource_path.is_empty() || self.resource_path.starts_with("res://")
    }

    /// Generates a unique identifier for a resource to be contained inside a PackedScene.
    pub fn generate_scene_unique_id() -> String {
        use std::time::{SystemTime, UNIX_EPOCH};

        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_nanos();

        // Generate a random component
        let random_part: u32 = (timestamp % u32::MAX as u128) as u32;

        // Convert to base-36 (letters a-z and numbers 0-9, but limited to a-y and 0-8 as per Godot)
        let mut result = String::new();
        let mut value = random_part;
        let charset = "0123456789abcdefghijklmnopqrstuvwxy";

        if value == 0 {
            result.push('0');
        } else {
            while value > 0 {
                let index = (value % 35) as usize; // 35 characters in charset
                result.push(charset.chars().nth(index).unwrap());
                value /= 35;
            }
        }

        result.chars().rev().collect()
    }

    /// Duplicates this resource, returning a new resource with its properties copied from the original.
    pub fn duplicate(&self, subresources: bool) -> Self {
        let mut duplicate = Self::new();
        duplicate.resource_name = self.resource_name.clone();
        duplicate.resource_local_to_scene = self.resource_local_to_scene;
        duplicate.resource_scene_unique_id = if subresources {
            Self::generate_scene_unique_id()
        } else {
            self.resource_scene_unique_id.clone()
        };
        duplicate.metadata = self.metadata.clone();

        // Note: In a real implementation, this would handle subresource duplication
        // based on the subresources parameter and property usage flags

        duplicate
    }

    /// Sets metadata for the resource.
    pub fn set_metadata(&mut self, key: String, value: String) {
        self.metadata.insert(key, value);
        self.mark_dirty();
    }

    /// Gets metadata for the resource.
    pub fn get_metadata(&self, key: &str) -> Option<&String> {
        self.metadata.get(key)
    }

    /// Removes metadata for the resource.
    pub fn remove_metadata(&mut self, key: &str) -> Option<String> {
        let result = self.metadata.remove(key);
        if result.is_some() {
            self.mark_dirty();
        }
        result
    }

    /// Returns all metadata keys.
    pub fn get_metadata_keys(&self) -> Vec<&String> {
        self.metadata.keys().collect()
    }

    /// Clears all metadata.
    pub fn clear_metadata(&mut self) {
        if !self.metadata.is_empty() {
            self.metadata.clear();
            self.mark_dirty();
        }
    }

    /// Takes over the path from another resource, potentially overriding an existing cache entry.
    pub fn take_over_path(&mut self, path: String) {
        // In a real implementation, this would handle resource cache management
        self.set_path(path);
    }

    /// Resets the resource state. Override in subclasses for custom behavior.
    pub fn reset_state(&mut self) {
        // Default implementation - subclasses should override this
        self.metadata.clear();
        self.mark_dirty();
    }

    /// Sets up the resource for local to scene usage. Override in subclasses for custom behavior.
    pub fn setup_local_to_scene(&mut self) {
        // Default implementation - subclasses should override this
        if self.resource_local_to_scene {
            // Perform any necessary setup for local to scene resources
        }
    }

    /// Emits a change signal. In a real implementation, this would notify observers.
    pub fn emit_changed(&self) {
        // In a real implementation, this would emit a signal to notify observers
        println!("Resource '{}' changed", self.resource_name);
    }

    /// Returns the local scene ID if this resource is local to scene.
    pub fn get_local_scene_id(&self) -> Option<u64> {
        self.local_scene_id
    }

    /// Sets the local scene ID for this resource.
    pub fn set_local_scene_id(&mut self, scene_id: Option<u64>) {
        self.local_scene_id = scene_id;
    }

    /// Returns a string representation of the resource.
    pub fn to_string(&self) -> String {
        if !self.resource_name.is_empty() {
            format!("Resource(name='{}', path='{}')", self.resource_name, self.resource_path)
        } else if !self.resource_path.is_empty() {
            format!("Resource(path='{}')", self.resource_path)
        } else {
            format!("Resource(id={})", self.instance_id)
        }
    }
}

impl Default for Resource {
    fn default() -> Self {
        Self::new()
    }
}

impl PartialEq for Resource {
    fn eq(&self, other: &Self) -> bool {
        self.instance_id == other.instance_id
    }
}

impl Eq for Resource {}

impl std::hash::Hash for Resource {
    fn hash<H: std::hash::Hasher>(&self, state: &mut H) {
        self.instance_id.hash(state);
    }
}