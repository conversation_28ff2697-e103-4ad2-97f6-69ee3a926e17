use std::collections::HashMap;
use std::sync::{Arc, RwLock};
use std::path::Path;
use super::Resource::{Resource, ResourceResult, ResourceError};

/// Global resource loader and cache manager.
/// 
/// ResourceLoader is responsible for loading resources from disk and managing a global cache
/// to ensure that resources are only loaded once and shared between all references.
/// 
/// # Examples
/// 
/// ```
/// use verturion::core::classes::resources::{ResourceLoader, Resource};
/// 
/// // Load a resource
/// let resource = ResourceLoader::load("res://my_resource.tres").unwrap();
/// 
/// // Check if a resource is cached
/// if ResourceLoader::has_cached("res://my_resource.tres") {
///     println!("Resource is already loaded");
/// }
/// ```
pub struct ResourceLoader {
    /// Global cache of loaded resources, indexed by path.
    cache: RwLock<HashMap<String, Arc<Resource>>>,
}

/// Trait for loading specific resource formats.
pub trait ResourceFormatLoader: Send + Sync {
    /// Returns the file extensions this loader can handle.
    fn get_recognized_extensions(&self) -> Vec<String>;
    
    /// Loads a resource from the given path.
    fn load(&self, path: &str) -> ResourceResult<Resource>;
    
    /// Returns true if this loader can handle the given path.
    fn handles_type(&self, path: &str) -> bool;
}

/// Default resource format loader for .tres files (Godot text resources).
pub struct TresFormatLoader;

impl ResourceFormatLoader for TresFormatLoader {
    fn get_recognized_extensions(&self) -> Vec<String> {
        vec!["tres".to_string()]
    }
    
    fn load(&self, path: &str) -> ResourceResult<Resource> {
        // In a real implementation, this would parse the .tres file format
        let mut resource = Resource::new();
        resource.set_path(path.to_string());
        resource.set_name(
            Path::new(path)
                .file_stem()
                .and_then(|s| s.to_str())
                .unwrap_or("Unknown")
                .to_string()
        );
        Ok(resource)
    }
    
    fn handles_type(&self, path: &str) -> bool {
        path.ends_with(".tres")
    }
}

/// Default resource format loader for .res files (Godot binary resources).
pub struct ResFormatLoader;

impl ResourceFormatLoader for ResFormatLoader {
    fn get_recognized_extensions(&self) -> Vec<String> {
        vec!["res".to_string()]
    }
    
    fn load(&self, path: &str) -> ResourceResult<Resource> {
        // In a real implementation, this would parse the .res file format
        let mut resource = Resource::new();
        resource.set_path(path.to_string());
        resource.set_name(
            Path::new(path)
                .file_stem()
                .and_then(|s| s.to_str())
                .unwrap_or("Unknown")
                .to_string()
        );
        Ok(resource)
    }
    
    fn handles_type(&self, path: &str) -> bool {
        path.ends_with(".res")
    }
}

impl ResourceLoader {
    /// Creates a new ResourceLoader.
    pub fn new() -> Self {
        Self {
            cache: RwLock::new(HashMap::new()),
        }
    }

    /// Loads a resource from the given path.
    pub fn load(&self, path: &str) -> ResourceResult<Arc<Resource>> {
        // Check if the resource is already cached
        {
            let cache = self.cache.read().unwrap();
            if let Some(resource) = cache.get(path) {
                return Ok(resource.clone());
            }
        }

        // Simple format detection based on file extension
        let resource = if path.ends_with(".tres") {
            TresFormatLoader.load(path)?
        } else if path.ends_with(".res") {
            ResFormatLoader.load(path)?
        } else {
            // Default to creating a basic resource
            let mut resource = Resource::new();
            resource.set_path(path.to_string());
            resource.set_name(
                Path::new(path)
                    .file_stem()
                    .and_then(|s| s.to_str())
                    .unwrap_or("Unknown")
                    .to_string()
            );
            resource
        };

        let arc_resource = Arc::new(resource);

        // Cache the resource
        {
            let mut cache = self.cache.write().unwrap();
            cache.insert(path.to_string(), arc_resource.clone());
        }

        Ok(arc_resource)
    }
    
    /// Returns true if the resource at the given path is cached.
    pub fn has_cached(&self, path: &str) -> bool {
        let cache = self.cache.read().unwrap();
        cache.contains_key(path)
    }
    
    /// Removes a resource from the cache.
    pub fn uncache(&self, path: &str) -> bool {
        let mut cache = self.cache.write().unwrap();
        cache.remove(path).is_some()
    }
    
    /// Clears all cached resources.
    pub fn clear_cache(&self) {
        let mut cache = self.cache.write().unwrap();
        cache.clear();
    }
    
    /// Returns the number of cached resources.
    pub fn get_cached_count(&self) -> usize {
        let cache = self.cache.read().unwrap();
        cache.len()
    }
    
    /// Returns a list of all cached resource paths.
    pub fn get_cached_paths(&self) -> Vec<String> {
        let cache = self.cache.read().unwrap();
        cache.keys().cloned().collect()
    }
    
    /// Preloads a resource (loads it into cache without returning it).
    pub fn preload(&self, path: &str) -> ResourceResult<()> {
        self.load(path)?;
        Ok(())
    }
    
    /// Returns true if the resource exists at the given path.
    pub fn exists(&self, path: &str) -> bool {
        // In a real implementation, this would check the file system
        Path::new(path).exists()
    }
    
    /// Returns the dependencies of a resource (other resources it references).
    pub fn get_dependencies(&self, _path: &str) -> ResourceResult<Vec<String>> {
        // In a real implementation, this would parse the resource file to find dependencies
        Ok(vec![])
    }
}

impl Default for ResourceLoader {
    fn default() -> Self {
        Self::new()
    }
}

// Global singleton instance
lazy_static::lazy_static! {
    static ref GLOBAL_RESOURCE_LOADER: ResourceLoader = ResourceLoader::new();
}

/// Global functions for resource loading (similar to Godot's global functions).
pub mod global {
    use super::*;
    
    /// Loads a resource from the given path using the global ResourceLoader.
    pub fn load(path: &str) -> ResourceResult<Arc<Resource>> {
        GLOBAL_RESOURCE_LOADER.load(path)
    }
    
    /// Preloads a resource using the global ResourceLoader.
    pub fn preload(path: &str) -> ResourceResult<()> {
        GLOBAL_RESOURCE_LOADER.preload(path)
    }
    
    /// Returns true if the resource is cached in the global ResourceLoader.
    pub fn has_cached(path: &str) -> bool {
        GLOBAL_RESOURCE_LOADER.has_cached(path)
    }
    
    /// Returns true if the resource exists at the given path.
    pub fn exists(path: &str) -> bool {
        GLOBAL_RESOURCE_LOADER.exists(path)
    }
}
