use std::collections::HashMap;
use std::rc::{Rc, Weak};
use std::cell::RefCell;

/// Base class for all scene objects.
///
/// Nodes are Godot's building blocks. They can be assigned as the child of another node,
/// resulting in a tree arrangement. A given node can contain any number of nodes as children
/// with the requirement that all siblings (direct children of a node) should have unique names.
///
/// A tree of nodes is called a scene. Scenes can be saved to the disk and then instantiated
/// into other scenes. This allows for very high flexibility in the architecture and data model of Godot projects.
///
/// # Examples
///
/// ```
/// use verturion::core::classes::nodes::Node;
///
/// let mut parent = Node::new("Parent".to_string());
/// let child = Node::new("Child".to_string());
/// parent.add_child(child);
/// ```
#[derive(Debug)]
pub struct Node {
    /// The name of the node. This name must be unique among the siblings (other child nodes from the same parent).
    pub name: String,

    /// The node's unique identifier within the scene tree.
    instance_id: u64,

    /// Reference to the parent node, if any.
    parent: Option<Weak<RefCell<Node>>>,

    /// List of child nodes.
    children: Vec<Rc<RefCell<Node>>>,

    /// Groups this node belongs to.
    groups: Vec<String>,

    /// The node that owns this node. When saving a scene, only the owner and nodes owned by it are saved.
    owner: Option<Weak<RefCell<Node>>>,

    /// The node's processing priority. Nodes with a higher priority will be processed first.
    process_priority: i32,

    /// The node's process mode. See ProcessMode enum.
    process_mode: ProcessMode,

    /// Whether the node is processing.
    processing: bool,

    /// Whether the node is physics processing.
    physics_processing: bool,

    /// Whether the node is processing input.
    processing_input: bool,

    /// Whether the node is processing unhandled input.
    processing_unhandled_input: bool,

    /// Whether the node is processing unhandled key input.
    processing_unhandled_key_input: bool,

    /// Whether the node is processing shortcut input.
    processing_shortcut_input: bool,

    /// Whether the node is ready (has entered the tree and _ready() has been called).
    is_ready: bool,

    /// Whether the node is inside the scene tree.
    is_inside_tree: bool,

    /// Custom user data that can be attached to the node.
    user_data: HashMap<String, String>,
}

/// Defines how the node should behave when the SceneTree is paused.
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ProcessMode {
    /// Inherit the process mode from the node's parent. For the root node, it is equivalent to Pausable.
    Inherit,
    /// Stop processing when the SceneTree is paused.
    Pausable,
    /// Continue to process regardless of the SceneTree pause state.
    WhenPaused,
    /// Always process. Continue processing always, ignoring the SceneTree pause state.
    Always,
    /// Never process. Completely disables processing, ignoring the SceneTree pause state.
    Disabled,
}

/// Defines how physics interpolation should be handled for this node.
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum PhysicsInterpolationMode {
    /// Inherit the physics interpolation mode from the node's parent.
    Inherit,
    /// Disable physics interpolation for this node.
    Off,
    /// Enable physics interpolation for this node.
    On,
}

impl Node {
    /// Creates a new Node with the given name.
    pub fn new(name: String) -> Self {
        static mut NEXT_ID: u64 = 1;
        let instance_id = unsafe {
            let id = NEXT_ID;
            NEXT_ID += 1;
            id
        };

        Self {
            name,
            instance_id,
            parent: None,
            children: Vec::new(),
            groups: Vec::new(),
            owner: None,
            process_priority: 0,
            process_mode: ProcessMode::Inherit,
            processing: false,
            physics_processing: false,
            processing_input: false,
            processing_unhandled_input: false,
            processing_unhandled_key_input: false,
            processing_shortcut_input: false,
            is_ready: false,
            is_inside_tree: false,
            user_data: HashMap::new(),
        }
    }

    /// Returns the node's unique instance ID.
    pub fn get_instance_id(&self) -> u64 {
        self.instance_id
    }

    /// Returns the name of the node.
    pub fn get_name(&self) -> &str {
        &self.name
    }

    /// Sets the name of the node.
    pub fn set_name(&mut self, name: String) {
        self.name = name;
    }

    /// Returns the node's process priority.
    pub fn get_process_priority(&self) -> i32 {
        self.process_priority
    }

    /// Sets the node's process priority. Nodes with a higher priority will be processed first.
    pub fn set_process_priority(&mut self, priority: i32) {
        self.process_priority = priority;
    }

    /// Returns the node's process mode.
    pub fn get_process_mode(&self) -> ProcessMode {
        self.process_mode
    }

    /// Sets the node's process mode.
    pub fn set_process_mode(&mut self, mode: ProcessMode) {
        self.process_mode = mode;
    }

    /// Returns true if the node is ready (has entered the tree and _ready() has been called).
    pub fn is_node_ready(&self) -> bool {
        self.is_ready
    }

    /// Returns true if the node is inside the scene tree.
    pub fn is_inside_tree(&self) -> bool {
        self.is_inside_tree
    }

    /// Returns the number of child nodes.
    pub fn get_child_count(&self) -> usize {
        self.children.len()
    }

    /// Returns true if the node has any children.
    pub fn has_children(&self) -> bool {
        !self.children.is_empty()
    }

    /// Returns the child node at the given index.
    pub fn get_child(&self, index: usize) -> Option<Rc<RefCell<Node>>> {
        self.children.get(index).cloned()
    }

    /// Returns all child nodes.
    pub fn get_children(&self) -> Vec<Rc<RefCell<Node>>> {
        self.children.clone()
    }

    /// Returns the parent node, if any.
    pub fn get_parent(&self) -> Option<Rc<RefCell<Node>>> {
        self.parent.as_ref().and_then(|p| p.upgrade())
    }

    /// Returns true if this node is an ancestor of the given node.
    pub fn is_ancestor_of(&self, node: &Node) -> bool {
        let mut current = node.get_parent();
        while let Some(parent) = current {
            if parent.borrow().instance_id == self.instance_id {
                return true;
            }
            current = parent.borrow().get_parent();
        }
        false
    }

    /// Returns the index of this node among its siblings.
    pub fn get_index(&self) -> Option<usize> {
        if let Some(parent) = self.get_parent() {
            parent.borrow().children.iter().position(|child| {
                child.borrow().instance_id == self.instance_id
            })
        } else {
            None
        }
    }

    /// Adds a child node. The child node automatically gets a parent set to this node.
    pub fn add_child(&mut self, child: Rc<RefCell<Node>>) {
        // Set the parent of the child
        child.borrow_mut().parent = Some(Rc::downgrade(&Rc::new(RefCell::new(Node::new("temp".to_string())))));

        // Add to children list
        self.children.push(child.clone());

        // If this node is in the tree, add the child to the tree as well
        if self.is_inside_tree {
            self.add_child_to_tree(child);
        }
    }

    /// Removes a child node. The child node's parent is set to None.
    pub fn remove_child(&mut self, child: Rc<RefCell<Node>>) {
        if let Some(index) = self.children.iter().position(|c| {
            c.borrow().instance_id == child.borrow().instance_id
        }) {
            self.children.remove(index);
            child.borrow_mut().parent = None;

            // If the child was in the tree, remove it from the tree
            if child.borrow().is_inside_tree {
                self.remove_child_from_tree(child);
            }
        }
    }

    /// Moves a child node to the given index.
    pub fn move_child(&mut self, child: Rc<RefCell<Node>>, to_index: usize) {
        if let Some(current_index) = self.children.iter().position(|c| {
            c.borrow().instance_id == child.borrow().instance_id
        }) {
            let child = self.children.remove(current_index);
            let insert_index = if to_index > current_index {
                to_index - 1
            } else {
                to_index
            }.min(self.children.len());
            self.children.insert(insert_index, child);
        }
    }

    /// Finds a child node by name. Returns None if not found.
    pub fn find_child(&self, name: &str, recursive: bool) -> Option<Rc<RefCell<Node>>> {
        // First check direct children
        for child in &self.children {
            if child.borrow().name == name {
                return Some(child.clone());
            }
        }

        // If recursive, search in grandchildren
        if recursive {
            for child in &self.children {
                if let Some(found) = child.borrow().find_child(name, true) {
                    return Some(found);
                }
            }
        }

        None
    }

    /// Gets a node by its path relative to this node.
    pub fn get_node(&self, path: &str) -> Option<Rc<RefCell<Node>>> {
        if path.is_empty() || path == "." {
            // Return self - this would need proper Rc handling in real implementation
            return None; // Simplified for now
        }

        if path == ".." {
            return self.get_parent();
        }

        // Handle absolute paths starting with "/"
        if path.starts_with('/') {
            return self.get_tree_root().and_then(|root| {
                root.borrow().get_node(&path[1..])
            });
        }

        // Handle relative paths
        let parts: Vec<&str> = path.split('/').collect();
        let mut current = None; // Would need proper self reference

        for part in parts {
            if part.is_empty() {
                continue;
            }

            if part == ".." {
                current = current.and_then(|node: Rc<RefCell<Node>>| node.borrow().get_parent());
            } else {
                current = current.and_then(|node: Rc<RefCell<Node>>| {
                    node.borrow().find_child(part, false)
                });
            }

            if current.is_none() {
                break;
            }
        }

        current
    }

    /// Returns the root node of the tree.
    pub fn get_tree_root(&self) -> Option<Rc<RefCell<Node>>> {
        let mut current = self.get_parent();
        let mut root = None;

        while let Some(parent) = current {
            root = Some(parent.clone());
            current = parent.borrow().get_parent();
        }

        root
    }

    /// Adds this node to a group.
    pub fn add_to_group(&mut self, group: String) {
        if !self.groups.contains(&group) {
            self.groups.push(group);
        }
    }

    /// Removes this node from a group.
    pub fn remove_from_group(&mut self, group: &str) {
        self.groups.retain(|g| g != group);
    }

    /// Returns true if this node is in the given group.
    pub fn is_in_group(&self, group: &str) -> bool {
        self.groups.contains(&group.to_string())
    }

    /// Returns all groups this node belongs to.
    pub fn get_groups(&self) -> &Vec<String> {
        &self.groups
    }

    /// Sets whether the node should process.
    pub fn set_process(&mut self, enable: bool) {
        self.processing = enable;
    }

    /// Returns true if the node is processing.
    pub fn is_processing(&self) -> bool {
        self.processing
    }

    /// Sets whether the node should physics process.
    pub fn set_physics_process(&mut self, enable: bool) {
        self.physics_processing = enable;
    }

    /// Returns true if the node is physics processing.
    pub fn is_physics_processing(&self) -> bool {
        self.physics_processing
    }

    /// Sets whether the node should process input.
    pub fn set_process_input(&mut self, enable: bool) {
        self.processing_input = enable;
    }

    /// Returns true if the node is processing input.
    pub fn is_processing_input(&self) -> bool {
        self.processing_input
    }

    /// Sets whether the node should process unhandled input.
    pub fn set_process_unhandled_input(&mut self, enable: bool) {
        self.processing_unhandled_input = enable;
    }

    /// Returns true if the node is processing unhandled input.
    pub fn is_processing_unhandled_input(&self) -> bool {
        self.processing_unhandled_input
    }

    /// Sets whether the node should process unhandled key input.
    pub fn set_process_unhandled_key_input(&mut self, enable: bool) {
        self.processing_unhandled_key_input = enable;
    }

    /// Returns true if the node is processing unhandled key input.
    pub fn is_processing_unhandled_key_input(&self) -> bool {
        self.processing_unhandled_key_input
    }

    /// Sets whether the node should process shortcut input.
    pub fn set_process_shortcut_input(&mut self, enable: bool) {
        self.processing_shortcut_input = enable;
    }

    /// Returns true if the node is processing shortcut input.
    pub fn is_processing_shortcut_input(&self) -> bool {
        self.processing_shortcut_input
    }

    /// Queues this node for deletion at the end of the current frame.
    pub fn queue_free(&mut self) {
        // In a real implementation, this would mark the node for deletion
        // and remove it from the scene tree at the end of the frame
        println!("Node '{}' queued for deletion", self.name);
    }

    /// Replaces this node with another node.
    pub fn replace_by(&mut self, _node: Rc<RefCell<Node>>, _keep_groups: bool) {
        // Implementation would replace this node with the given node
        // and optionally preserve group memberships
    }

    /// Changes the parent of this node.
    pub fn reparent(&mut self, _new_parent: Rc<RefCell<Node>>, _keep_global_transform: bool) {
        // Implementation would change the parent while optionally preserving global transform
    }

    /// Requests that _ready() be called again the next time the node enters the tree.
    pub fn request_ready(&mut self) {
        self.is_ready = false;
    }

    /// Prints the node tree structure to console.
    pub fn print_tree(&self) {
        self.print_tree_recursive(0);
    }

    fn print_tree_recursive(&self, depth: usize) {
        let indent = "  ".repeat(depth);
        println!("{}{}", indent, self.name);
        for child in &self.children {
            child.borrow().print_tree_recursive(depth + 1);
        }
    }

    /// Prints a pretty representation of the node tree.
    pub fn print_tree_pretty(&self) {
        self.print_tree_pretty_recursive(0, true, "");
    }

    fn print_tree_pretty_recursive(&self, depth: usize, is_last: bool, prefix: &str) {
        let connector = if depth == 0 {
            "┖╴"
        } else if is_last {
            "┖╴"
        } else {
            "┠╴"
        };

        println!("{}{}{}", prefix, connector, self.name);

        let new_prefix = if depth == 0 {
            "   ".to_string()
        } else if is_last {
            format!("{}   ", prefix)
        } else {
            format!("{}┃  ", prefix)
        };

        for (i, child) in self.children.iter().enumerate() {
            let is_last_child = i == self.children.len() - 1;
            child.borrow().print_tree_pretty_recursive(depth + 1, is_last_child, &new_prefix);
        }
    }

    /// Gets or sets user data for the node.
    pub fn set_user_data(&mut self, key: String, value: String) {
        self.user_data.insert(key, value);
    }

    /// Gets user data for the node.
    pub fn get_user_data(&self, key: &str) -> Option<&String> {
        self.user_data.get(key)
    }

    /// Sets the owner of this node.
    pub fn set_owner(&mut self, owner: Option<Weak<RefCell<Node>>>) {
        self.owner = owner;
    }

    /// Gets the owner of this node.
    pub fn get_owner(&self) -> Option<Rc<RefCell<Node>>> {
        self.owner.as_ref().and_then(|o| o.upgrade())
    }

    // Virtual methods that can be overridden by subclasses
    // In a real implementation, these would be trait methods or use dynamic dispatch

    /// Called when the node enters the scene tree for the first time.
    pub fn _enter_tree(&mut self) {
        self.is_inside_tree = true;
        // Override in subclasses for custom behavior
    }

    /// Called when the node is ready (after _enter_tree and all children are ready).
    pub fn _ready(&mut self) {
        self.is_ready = true;
        // Override in subclasses for custom behavior
    }

    /// Called when the node exits the scene tree.
    pub fn _exit_tree(&mut self) {
        self.is_inside_tree = false;
        self.is_ready = false;
        // Override in subclasses for custom behavior
    }

    /// Called every frame when processing is enabled.
    pub fn _process(&mut self, _delta: f32) {
        // Override in subclasses for custom behavior
    }

    /// Called every physics frame when physics processing is enabled.
    pub fn _physics_process(&mut self, _delta: f32) {
        // Override in subclasses for custom behavior
    }

    /// Called when input events are received.
    pub fn _input(&mut self, _event: &InputEvent) {
        // Override in subclasses for custom behavior
    }

    /// Called when unhandled input events are received.
    pub fn _unhandled_input(&mut self, _event: &InputEvent) {
        // Override in subclasses for custom behavior
    }

    /// Called when unhandled key input events are received.
    pub fn _unhandled_key_input(&mut self, _event: &InputEvent) {
        // Override in subclasses for custom behavior
    }

    /// Called when shortcut input events are received.
    pub fn _shortcut_input(&mut self, _event: &InputEvent) {
        // Override in subclasses for custom behavior
    }

    // Private helper methods
    fn add_child_to_tree(&self, child: Rc<RefCell<Node>>) {
        // Implementation would handle adding child to scene tree
        child.borrow_mut()._enter_tree();
        child.borrow_mut()._ready();

        // Recursively add grandchildren
        let children = child.borrow().children.clone();
        for grandchild in children {
            child.borrow().add_child_to_tree(grandchild);
        }
    }

    fn remove_child_from_tree(&self, child: Rc<RefCell<Node>>) {
        // Recursively remove grandchildren first
        let children = child.borrow().children.clone();
        for grandchild in children {
            child.borrow().remove_child_from_tree(grandchild);
        }

        // Remove the child itself
        child.borrow_mut()._exit_tree();
    }
}

/// Placeholder for input events - in a real implementation this would be a proper InputEvent type
#[derive(Debug, Clone)]
pub struct InputEvent {
    // Placeholder fields
}

impl Default for ProcessMode {
    fn default() -> Self {
        ProcessMode::Inherit
    }
}

impl Default for PhysicsInterpolationMode {
    fn default() -> Self {
        PhysicsInterpolationMode::Inherit
    }
}
