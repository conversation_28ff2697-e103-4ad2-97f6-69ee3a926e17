/// A 3D vector using floating-point coordinates.
///
/// A 3-element structure that can be used to represent 3D coordinates or any other triplet of numeric values.
/// It uses floating-point coordinates of 32-bit precision, unlike Vector3i which uses integers.
///
/// # Examples
///
/// ```
/// use verturion::core::classes::variants::Vector3;
///
/// let v1 = Vector3::new(1.0, 2.0, 3.0);
/// let v2 = Vector3::new(4.0, 5.0, 6.0);
/// let sum = v1 + v2;
/// assert_eq!(sum, Vector3::new(5.0, 7.0, 9.0));
/// ```
#[derive(Debug, <PERSON>lone, Copy, PartialEq)]
#[repr(C)]
pub struct Vector3 {
    pub x: f32,
    pub y: f32,
    pub z: f32,
}

impl Vector3 {
    /// Zero vector, a vector with all components set to 0.
    pub const ZERO: Vector3 = Vector3 { x: 0.0, y: 0.0, z: 0.0 };

    /// One vector, a vector with all components set to 1.
    pub const ONE: Vector3 = Vector3 { x: 1.0, y: 1.0, z: 1.0 };

    /// Infinity vector, a vector with all components set to positive infinity.
    pub const INF: Vector3 = Vector3 { x: f32::INFINITY, y: f32::INFINITY, z: f32::INFINITY };

    /// Left unit vector. Represents the direction of decreasing X.
    pub const LEFT: Vector3 = Vector3 { x: -1.0, y: 0.0, z: 0.0 };

    /// Right unit vector. Represents the direction of increasing X.
    pub const RIGHT: Vector3 = Vector3 { x: 1.0, y: 0.0, z: 0.0 };

    /// Up unit vector. Y is up in Godot's 3D coordinate system.
    pub const UP: Vector3 = Vector3 { x: 0.0, y: 1.0, z: 0.0 };

    /// Down unit vector. Y is up in Godot's 3D coordinate system.
    pub const DOWN: Vector3 = Vector3 { x: 0.0, y: -1.0, z: 0.0 };

    /// Forward unit vector. Represents the direction of decreasing Z.
    pub const FORWARD: Vector3 = Vector3 { x: 0.0, y: 0.0, z: -1.0 };

    /// Back unit vector. Represents the direction of increasing Z.
    pub const BACK: Vector3 = Vector3 { x: 0.0, y: 0.0, z: 1.0 };

    /// Constructs a new Vector3 from x, y, and z.
    #[inline]
    pub const fn new(x: f32, y: f32, z: f32) -> Self {
        Self { x, y, z }
    }

    /// Returns the length (magnitude) of this vector.
    #[inline]
    pub fn length(self) -> f32 {
        (self.x * self.x + self.y * self.y + self.z * self.z).sqrt()
    }

    /// Returns the squared length (squared magnitude) of this vector.
    /// This method runs faster than length(), so prefer it if you need to compare vectors
    /// or need the squared distance for some formula.
    #[inline]
    pub fn length_squared(self) -> f32 {
        self.x * self.x + self.y * self.y + self.z * self.z
    }

    /// Returns the result of scaling the vector to unit length.
    /// Equivalent to `v / v.length()`. Returns `Vector3::ZERO` if `v.length() == 0`.
    #[inline]
    pub fn normalized(self) -> Self {
        let length = self.length();
        if length == 0.0 {
            Self::ZERO
        } else {
            self / length
        }
    }

    /// Returns true if the vector is normalized, i.e. its length is approximately equal to 1.
    #[inline]
    pub fn is_normalized(self) -> bool {
        (self.length_squared() - 1.0).abs() < f32::EPSILON
    }

    /// Returns the dot product of this vector and `with`.
    /// This can be used to compare the angle between two vectors.
    #[inline]
    pub fn dot(self, with: Vector3) -> f32 {
        self.x * with.x + self.y * with.y + self.z * with.z
    }

    /// Returns the cross product of this vector and `with`.
    #[inline]
    pub fn cross(self, with: Vector3) -> Self {
        Self {
            x: self.y * with.z - self.z * with.y,
            y: self.z * with.x - self.x * with.z,
            z: self.x * with.y - self.y * with.x,
        }
    }

    /// Returns the distance between this vector and `to`.
    #[inline]
    pub fn distance_to(self, to: Vector3) -> f32 {
        (to - self).length()
    }

    /// Returns the squared distance between this vector and `to`.
    /// This method runs faster than distance_to(), so prefer it if you need to compare vectors
    /// or need the squared distance for some formula.
    #[inline]
    pub fn distance_squared_to(self, to: Vector3) -> f32 {
        (to - self).length_squared()
    }

    /// Returns the normalized vector pointing from this vector to `to`.
    /// This is equivalent to using `(b - a).normalized()`.
    #[inline]
    pub fn direction_to(self, to: Vector3) -> Self {
        (to - self).normalized()
    }

    /// Returns the result of the linear interpolation between this vector and `to` by amount `weight`.
    /// `weight` is on the range of 0.0 to 1.0, representing the amount of interpolation.
    #[inline]
    pub fn lerp(self, to: Vector3, weight: f32) -> Self {
        self + (to - self) * weight
    }

    /// Returns the result of spherical linear interpolation between this vector and `to`, by amount `weight`.
    /// `weight` is on the range of 0.0 to 1.0, representing the amount of interpolation.
    #[inline]
    pub fn slerp(self, to: Vector3, weight: f32) -> Self {
        let start_length_sq = self.length_squared();
        let end_length_sq = to.length_squared();
        if start_length_sq == 0.0 || end_length_sq == 0.0 {
            return self.lerp(to, weight);
        }
        let start_length = start_length_sq.sqrt();
        let end_length = end_length_sq.sqrt();
        let result_length = start_length + (end_length - start_length) * weight;
        let angle = self.angle_to(to);
        if angle.abs() < f32::EPSILON {
            return self.lerp(to, weight);
        }
        let axis = self.cross(to).normalized();
        self.rotated(axis, angle * weight) * (result_length / start_length)
    }

    /// Returns the angle to the given vector, in radians.
    #[inline]
    pub fn angle_to(self, to: Vector3) -> f32 {
        self.cross(to).length().atan2(self.dot(to))
    }

    /// Returns the signed angle to the given vector, in radians.
    /// The sign of the angle is positive in a counter-clockwise direction and negative in a clockwise direction when viewed from the side specified by the `axis`.
    #[inline]
    pub fn signed_angle_to(self, to: Vector3, axis: Vector3) -> f32 {
        let cross_product = self.cross(to);
        let unsigned_angle = cross_product.length().atan2(self.dot(to));
        let sign = cross_product.dot(axis);
        if sign < 0.0 { -unsigned_angle } else { unsigned_angle }
    }

    /// Returns the vector rotated by `angle` radians around the given `axis`.
    #[inline]
    pub fn rotated(self, axis: Vector3, angle: f32) -> Self {
        let axis = axis.normalized();
        let cos_angle = angle.cos();
        let sin_angle = angle.sin();
        let one_minus_cos = 1.0 - cos_angle;

        // Rodrigues' rotation formula
        self * cos_angle
            + axis.cross(self) * sin_angle
            + axis * axis.dot(self) * one_minus_cos
    }

    /// Returns the vector with each component set to one or negative one, depending on the signs of the components,
    /// or zero if the component is zero, by calling `sign()` on each component.
    #[inline]
    pub fn sign(self) -> Self {
        Self {
            x: if self.x > 0.0 { 1.0 } else if self.x < 0.0 { -1.0 } else { 0.0 },
            y: if self.y > 0.0 { 1.0 } else if self.y < 0.0 { -1.0 } else { 0.0 },
            z: if self.z > 0.0 { 1.0 } else if self.z < 0.0 { -1.0 } else { 0.0 },
        }
    }

    /// Returns the vector with all components rounded to the nearest integer, with halfway cases rounded away from zero.
    #[inline]
    pub fn round(self) -> Self {
        Self {
            x: self.x.round(),
            y: self.y.round(),
            z: self.z.round(),
        }
    }

    /// Returns the vector with all components rounded down (towards negative infinity).
    #[inline]
    pub fn floor(self) -> Self {
        Self {
            x: self.x.floor(),
            y: self.y.floor(),
            z: self.z.floor(),
        }
    }

    /// Returns the vector with all components rounded up (towards positive infinity).
    #[inline]
    pub fn ceil(self) -> Self {
        Self {
            x: self.x.ceil(),
            y: self.y.ceil(),
            z: self.z.ceil(),
        }
    }

    /// Returns a new vector with all components in absolute value (i.e. positive).
    #[inline]
    pub fn abs(self) -> Self {
        Self {
            x: self.x.abs(),
            y: self.y.abs(),
            z: self.z.abs(),
        }
    }

    /// Returns the vector projected onto the vector `b`.
    #[inline]
    pub fn project(self, onto: Vector3) -> Self {
        onto * (self.dot(onto) / onto.dot(onto))
    }

    /// Returns the vector reflected from a plane defined by the given normal.
    #[inline]
    pub fn reflect(self, normal: Vector3) -> Self {
        self - normal * 2.0 * self.dot(normal)
    }

    /// Returns the vector "bounced off" from a plane defined by the given normal.
    #[inline]
    pub fn bounce(self, normal: Vector3) -> Self {
        -self.reflect(normal)
    }

    /// Returns the vector slid along a plane defined by the given normal.
    #[inline]
    pub fn slide(self, normal: Vector3) -> Self {
        self - normal * self.dot(normal)
    }

    /// Returns true if this vector and `to` are approximately equal, by running `@GlobalScope.is_equal_approx` on each component.
    #[inline]
    pub fn is_equal_approx(self, to: Vector3) -> bool {
        (self.x - to.x).abs() < f32::EPSILON
            && (self.y - to.y).abs() < f32::EPSILON
            && (self.z - to.z).abs() < f32::EPSILON
    }

    /// Returns true if this vector's values are approximately zero, by running `@GlobalScope.is_zero_approx` on each component.
    #[inline]
    pub fn is_zero_approx(self) -> bool {
        self.x.abs() < f32::EPSILON && self.y.abs() < f32::EPSILON && self.z.abs() < f32::EPSILON
    }

    /// Returns true if the vector is finite, by calling `@GlobalScope.is_finite` on each component.
    #[inline]
    pub fn is_finite(self) -> bool {
        self.x.is_finite() && self.y.is_finite() && self.z.is_finite()
    }
}

// Operator implementations for Vector3
impl std::ops::Add for Vector3 {
    type Output = Self;

    #[inline]
    fn add(self, rhs: Self) -> Self::Output {
        Self {
            x: self.x + rhs.x,
            y: self.y + rhs.y,
            z: self.z + rhs.z,
        }
    }
}

impl std::ops::AddAssign for Vector3 {
    #[inline]
    fn add_assign(&mut self, rhs: Self) {
        self.x += rhs.x;
        self.y += rhs.y;
        self.z += rhs.z;
    }
}

impl std::ops::Sub for Vector3 {
    type Output = Self;

    #[inline]
    fn sub(self, rhs: Self) -> Self::Output {
        Self {
            x: self.x - rhs.x,
            y: self.y - rhs.y,
            z: self.z - rhs.z,
        }
    }
}

impl std::ops::SubAssign for Vector3 {
    #[inline]
    fn sub_assign(&mut self, rhs: Self) {
        self.x -= rhs.x;
        self.y -= rhs.y;
        self.z -= rhs.z;
    }
}

impl std::ops::Mul<Vector3> for Vector3 {
    type Output = Self;

    #[inline]
    fn mul(self, rhs: Vector3) -> Self::Output {
        Self {
            x: self.x * rhs.x,
            y: self.y * rhs.y,
            z: self.z * rhs.z,
        }
    }
}

impl std::ops::Mul<f32> for Vector3 {
    type Output = Self;

    #[inline]
    fn mul(self, rhs: f32) -> Self::Output {
        Self {
            x: self.x * rhs,
            y: self.y * rhs,
            z: self.z * rhs,
        }
    }
}

impl std::ops::Mul<Vector3> for f32 {
    type Output = Vector3;

    #[inline]
    fn mul(self, rhs: Vector3) -> Self::Output {
        Vector3 {
            x: self * rhs.x,
            y: self * rhs.y,
            z: self * rhs.z,
        }
    }
}

impl std::ops::MulAssign<Vector3> for Vector3 {
    #[inline]
    fn mul_assign(&mut self, rhs: Vector3) {
        self.x *= rhs.x;
        self.y *= rhs.y;
        self.z *= rhs.z;
    }
}

impl std::ops::MulAssign<f32> for Vector3 {
    #[inline]
    fn mul_assign(&mut self, rhs: f32) {
        self.x *= rhs;
        self.y *= rhs;
        self.z *= rhs;
    }
}

impl std::ops::Div<Vector3> for Vector3 {
    type Output = Self;

    #[inline]
    fn div(self, rhs: Vector3) -> Self::Output {
        Self {
            x: self.x / rhs.x,
            y: self.y / rhs.y,
            z: self.z / rhs.z,
        }
    }
}

impl std::ops::Div<f32> for Vector3 {
    type Output = Self;

    #[inline]
    fn div(self, rhs: f32) -> Self::Output {
        Self {
            x: self.x / rhs,
            y: self.y / rhs,
            z: self.z / rhs,
        }
    }
}

impl std::ops::DivAssign<Vector3> for Vector3 {
    #[inline]
    fn div_assign(&mut self, rhs: Vector3) {
        self.x /= rhs.x;
        self.y /= rhs.y;
        self.z /= rhs.z;
    }
}

impl std::ops::DivAssign<f32> for Vector3 {
    #[inline]
    fn div_assign(&mut self, rhs: f32) {
        self.x /= rhs;
        self.y /= rhs;
        self.z /= rhs;
    }
}

impl std::ops::Neg for Vector3 {
    type Output = Self;

    #[inline]
    fn neg(self) -> Self::Output {
        Self {
            x: -self.x,
            y: -self.y,
            z: -self.z,
        }
    }
}

impl std::ops::Index<usize> for Vector3 {
    type Output = f32;

    #[inline]
    fn index(&self, index: usize) -> &Self::Output {
        match index {
            0 => &self.x,
            1 => &self.y,
            2 => &self.z,
            _ => panic!("Vector3 index out of bounds: {}", index),
        }
    }
}

impl std::ops::IndexMut<usize> for Vector3 {
    #[inline]
    fn index_mut(&mut self, index: usize) -> &mut Self::Output {
        match index {
            0 => &mut self.x,
            1 => &mut self.y,
            2 => &mut self.z,
            _ => panic!("Vector3 index out of bounds: {}", index),
        }
    }
}
