/// A color represented in RGBA format.
///
/// A color represented in RGBA format by a red (r), green (g), blue (b), and alpha (a) component.
/// Each component is a 32-bit floating-point value, usually ranging from 0.0 to 1.0.
/// Some properties (such as CanvasItem.modulate) may support values greater than 1.0, for overbright or HDR colors.
///
/// # Examples
///
/// ```
/// use verturion::core::classes::variants::Color;
///
/// let red = Color::new(1.0, 0.0, 0.0, 1.0);
/// let green = Color::GREEN;
/// let mixed = red.lerp(green, 0.5);
/// ```
#[derive(Debug, <PERSON>lone, Copy, PartialEq)]
#[repr(C)]
pub struct Color {
    pub r: f32,
    pub g: f32,
    pub b: f32,
    pub a: f32,
}

impl Color {
    /// Alice blue color.
    pub const ALICE_BLUE: Color = Color { r: 0.941, g: 0.973, b: 1.0, a: 1.0 };

    /// Antique white color.
    pub const ANTIQUE_WHITE: Color = Color { r: 0.98, g: 0.922, b: 0.843, a: 1.0 };

    /// Aqua color.
    pub const AQUA: Color = Color { r: 0.0, g: 1.0, b: 1.0, a: 1.0 };

    /// Aquamarine color.
    pub const AQUAMARINE: Color = Color { r: 0.498, g: 1.0, b: 0.831, a: 1.0 };

    /// Azure color.
    pub const AZURE: Color = Color { r: 0.941, g: 1.0, b: 1.0, a: 1.0 };

    /// Beige color.
    pub const BEIGE: Color = Color { r: 0.961, g: 0.961, b: 0.863, a: 1.0 };

    /// Bisque color.
    pub const BISQUE: Color = Color { r: 1.0, g: 0.894, b: 0.769, a: 1.0 };

    /// Black color.
    pub const BLACK: Color = Color { r: 0.0, g: 0.0, b: 0.0, a: 1.0 };

    /// Blanched almond color.
    pub const BLANCHED_ALMOND: Color = Color { r: 1.0, g: 0.922, b: 0.804, a: 1.0 };

    /// Blue color.
    pub const BLUE: Color = Color { r: 0.0, g: 0.0, b: 1.0, a: 1.0 };

    /// Blue violet color.
    pub const BLUE_VIOLET: Color = Color { r: 0.541, g: 0.169, b: 0.886, a: 1.0 };

    /// Brown color.
    pub const BROWN: Color = Color { r: 0.647, g: 0.165, b: 0.165, a: 1.0 };

    /// Burlywood color.
    pub const BURLYWOOD: Color = Color { r: 0.871, g: 0.722, b: 0.529, a: 1.0 };

    /// Cadet blue color.
    pub const CADET_BLUE: Color = Color { r: 0.373, g: 0.620, b: 0.627, a: 1.0 };

    /// Chartreuse color.
    pub const CHARTREUSE: Color = Color { r: 0.498, g: 1.0, b: 0.0, a: 1.0 };

    /// Chocolate color.
    pub const CHOCOLATE: Color = Color { r: 0.824, g: 0.412, b: 0.118, a: 1.0 };

    /// Coral color.
    pub const CORAL: Color = Color { r: 1.0, g: 0.498, b: 0.314, a: 1.0 };

    /// Cornflower blue color.
    pub const CORNFLOWER_BLUE: Color = Color { r: 0.392, g: 0.584, b: 0.929, a: 1.0 };

    /// Cornsilk color.
    pub const CORNSILK: Color = Color { r: 1.0, g: 0.973, b: 0.863, a: 1.0 };

    /// Crimson color.
    pub const CRIMSON: Color = Color { r: 0.863, g: 0.078, b: 0.235, a: 1.0 };

    /// Cyan color.
    pub const CYAN: Color = Color { r: 0.0, g: 1.0, b: 1.0, a: 1.0 };

    /// Dark blue color.
    pub const DARK_BLUE: Color = Color { r: 0.0, g: 0.0, b: 0.545, a: 1.0 };

    /// Dark cyan color.
    pub const DARK_CYAN: Color = Color { r: 0.0, g: 0.545, b: 0.545, a: 1.0 };

    /// Dark goldenrod color.
    pub const DARK_GOLDENROD: Color = Color { r: 0.722, g: 0.525, b: 0.043, a: 1.0 };

    /// Dark gray color.
    pub const DARK_GRAY: Color = Color { r: 0.663, g: 0.663, b: 0.663, a: 1.0 };

    /// Dark green color.
    pub const DARK_GREEN: Color = Color { r: 0.0, g: 0.392, b: 0.0, a: 1.0 };

    /// Dark khaki color.
    pub const DARK_KHAKI: Color = Color { r: 0.741, g: 0.718, b: 0.420, a: 1.0 };

    /// Dark magenta color.
    pub const DARK_MAGENTA: Color = Color { r: 0.545, g: 0.0, b: 0.545, a: 1.0 };

    /// Dark olive green color.
    pub const DARK_OLIVE_GREEN: Color = Color { r: 0.333, g: 0.420, b: 0.184, a: 1.0 };

    /// Dark orange color.
    pub const DARK_ORANGE: Color = Color { r: 1.0, g: 0.549, b: 0.0, a: 1.0 };

    /// Dark orchid color.
    pub const DARK_ORCHID: Color = Color { r: 0.600, g: 0.196, b: 0.800, a: 1.0 };

    /// Dark red color.
    pub const DARK_RED: Color = Color { r: 0.545, g: 0.0, b: 0.0, a: 1.0 };

    /// Dark salmon color.
    pub const DARK_SALMON: Color = Color { r: 0.914, g: 0.588, b: 0.478, a: 1.0 };

    /// Dark sea green color.
    pub const DARK_SEA_GREEN: Color = Color { r: 0.561, g: 0.737, b: 0.561, a: 1.0 };

    /// Dark slate blue color.
    pub const DARK_SLATE_BLUE: Color = Color { r: 0.282, g: 0.239, b: 0.545, a: 1.0 };

    /// Dark slate gray color.
    pub const DARK_SLATE_GRAY: Color = Color { r: 0.184, g: 0.310, b: 0.310, a: 1.0 };

    /// Dark turquoise color.
    pub const DARK_TURQUOISE: Color = Color { r: 0.0, g: 0.808, b: 0.820, a: 1.0 };

    /// Dark violet color.
    pub const DARK_VIOLET: Color = Color { r: 0.580, g: 0.0, b: 0.827, a: 1.0 };

    /// Deep pink color.
    pub const DEEP_PINK: Color = Color { r: 1.0, g: 0.078, b: 0.576, a: 1.0 };

    /// Deep sky blue color.
    pub const DEEP_SKY_BLUE: Color = Color { r: 0.0, g: 0.749, b: 1.0, a: 1.0 };

    /// Dim gray color.
    pub const DIM_GRAY: Color = Color { r: 0.412, g: 0.412, b: 0.412, a: 1.0 };

    /// Dodger blue color.
    pub const DODGER_BLUE: Color = Color { r: 0.118, g: 0.565, b: 1.0, a: 1.0 };

    /// Firebrick color.
    pub const FIREBRICK: Color = Color { r: 0.698, g: 0.133, b: 0.133, a: 1.0 };

    /// Floral white color.
    pub const FLORAL_WHITE: Color = Color { r: 1.0, g: 0.980, b: 0.941, a: 1.0 };

    /// Forest green color.
    pub const FOREST_GREEN: Color = Color { r: 0.133, g: 0.545, b: 0.133, a: 1.0 };

    /// Fuchsia color.
    pub const FUCHSIA: Color = Color { r: 1.0, g: 0.0, b: 1.0, a: 1.0 };

    /// Gainsboro color.
    pub const GAINSBORO: Color = Color { r: 0.863, g: 0.863, b: 0.863, a: 1.0 };

    /// Ghost white color.
    pub const GHOST_WHITE: Color = Color { r: 0.973, g: 0.973, b: 1.0, a: 1.0 };

    /// Gold color.
    pub const GOLD: Color = Color { r: 1.0, g: 0.843, b: 0.0, a: 1.0 };

    /// Goldenrod color.
    pub const GOLDENROD: Color = Color { r: 0.855, g: 0.647, b: 0.125, a: 1.0 };

    /// Gray color.
    pub const GRAY: Color = Color { r: 0.502, g: 0.502, b: 0.502, a: 1.0 };

    /// Green color.
    pub const GREEN: Color = Color { r: 0.0, g: 0.502, b: 0.0, a: 1.0 };

    /// Green yellow color.
    pub const GREEN_YELLOW: Color = Color { r: 0.678, g: 1.0, b: 0.184, a: 1.0 };

    /// Honeydew color.
    pub const HONEYDEW: Color = Color { r: 0.941, g: 1.0, b: 0.941, a: 1.0 };

    /// Hot pink color.
    pub const HOT_PINK: Color = Color { r: 1.0, g: 0.412, b: 0.706, a: 1.0 };

    /// Indian red color.
    pub const INDIAN_RED: Color = Color { r: 0.804, g: 0.361, b: 0.361, a: 1.0 };

    /// Indigo color.
    pub const INDIGO: Color = Color { r: 0.294, g: 0.0, b: 0.510, a: 1.0 };

    /// Ivory color.
    pub const IVORY: Color = Color { r: 1.0, g: 1.0, b: 0.941, a: 1.0 };

    /// Khaki color.
    pub const KHAKI: Color = Color { r: 0.941, g: 0.902, b: 0.549, a: 1.0 };

    /// Lavender color.
    pub const LAVENDER: Color = Color { r: 0.902, g: 0.902, b: 0.980, a: 1.0 };

    /// Lavender blush color.
    pub const LAVENDER_BLUSH: Color = Color { r: 1.0, g: 0.941, b: 0.961, a: 1.0 };

    /// Lawn green color.
    pub const LAWN_GREEN: Color = Color { r: 0.486, g: 0.988, b: 0.0, a: 1.0 };

    /// Lemon chiffon color.
    pub const LEMON_CHIFFON: Color = Color { r: 1.0, g: 0.980, b: 0.804, a: 1.0 };

    /// Light blue color.
    pub const LIGHT_BLUE: Color = Color { r: 0.678, g: 0.847, b: 0.902, a: 1.0 };

    /// Light coral color.
    pub const LIGHT_CORAL: Color = Color { r: 0.941, g: 0.502, b: 0.502, a: 1.0 };

    /// Light cyan color.
    pub const LIGHT_CYAN: Color = Color { r: 0.878, g: 1.0, b: 1.0, a: 1.0 };

    /// Light goldenrod color.
    pub const LIGHT_GOLDENROD: Color = Color { r: 0.980, g: 0.980, b: 0.824, a: 1.0 };

    /// Light gray color.
    pub const LIGHT_GRAY: Color = Color { r: 0.827, g: 0.827, b: 0.827, a: 1.0 };

    /// Light green color.
    pub const LIGHT_GREEN: Color = Color { r: 0.565, g: 0.933, b: 0.565, a: 1.0 };

    /// Light pink color.
    pub const LIGHT_PINK: Color = Color { r: 1.0, g: 0.714, b: 0.757, a: 1.0 };

    /// Light salmon color.
    pub const LIGHT_SALMON: Color = Color { r: 1.0, g: 0.627, b: 0.478, a: 1.0 };

    /// Light sea green color.
    pub const LIGHT_SEA_GREEN: Color = Color { r: 0.125, g: 0.698, b: 0.667, a: 1.0 };

    /// Light sky blue color.
    pub const LIGHT_SKY_BLUE: Color = Color { r: 0.529, g: 0.808, b: 0.980, a: 1.0 };

    /// Light slate gray color.
    pub const LIGHT_SLATE_GRAY: Color = Color { r: 0.467, g: 0.533, b: 0.600, a: 1.0 };

    /// Light steel blue color.
    pub const LIGHT_STEEL_BLUE: Color = Color { r: 0.690, g: 0.769, b: 0.871, a: 1.0 };

    /// Light yellow color.
    pub const LIGHT_YELLOW: Color = Color { r: 1.0, g: 1.0, b: 0.878, a: 1.0 };

    /// Lime color.
    pub const LIME: Color = Color { r: 0.0, g: 1.0, b: 0.0, a: 1.0 };

    /// Lime green color.
    pub const LIME_GREEN: Color = Color { r: 0.196, g: 0.804, b: 0.196, a: 1.0 };

    /// Linen color.
    pub const LINEN: Color = Color { r: 0.980, g: 0.941, b: 0.902, a: 1.0 };

    /// Magenta color.
    pub const MAGENTA: Color = Color { r: 1.0, g: 0.0, b: 1.0, a: 1.0 };

    /// Maroon color.
    pub const MAROON: Color = Color { r: 0.502, g: 0.0, b: 0.0, a: 1.0 };

    /// Medium aquamarine color.
    pub const MEDIUM_AQUAMARINE: Color = Color { r: 0.400, g: 0.804, b: 0.667, a: 1.0 };

    /// Medium blue color.
    pub const MEDIUM_BLUE: Color = Color { r: 0.0, g: 0.0, b: 0.804, a: 1.0 };

    /// Medium orchid color.
    pub const MEDIUM_ORCHID: Color = Color { r: 0.729, g: 0.333, b: 0.827, a: 1.0 };

    /// Medium purple color.
    pub const MEDIUM_PURPLE: Color = Color { r: 0.576, g: 0.439, b: 0.859, a: 1.0 };

    /// Medium sea green color.
    pub const MEDIUM_SEA_GREEN: Color = Color { r: 0.235, g: 0.702, b: 0.443, a: 1.0 };

    /// Medium slate blue color.
    pub const MEDIUM_SLATE_BLUE: Color = Color { r: 0.482, g: 0.408, b: 0.933, a: 1.0 };

    /// Medium spring green color.
    pub const MEDIUM_SPRING_GREEN: Color = Color { r: 0.0, g: 0.980, b: 0.604, a: 1.0 };

    /// Medium turquoise color.
    pub const MEDIUM_TURQUOISE: Color = Color { r: 0.282, g: 0.820, b: 0.800, a: 1.0 };

    /// Medium violet red color.
    pub const MEDIUM_VIOLET_RED: Color = Color { r: 0.780, g: 0.082, b: 0.522, a: 1.0 };

    /// Midnight blue color.
    pub const MIDNIGHT_BLUE: Color = Color { r: 0.098, g: 0.098, b: 0.439, a: 1.0 };

    /// Mint cream color.
    pub const MINT_CREAM: Color = Color { r: 0.961, g: 1.0, b: 0.980, a: 1.0 };

    /// Misty rose color.
    pub const MISTY_ROSE: Color = Color { r: 1.0, g: 0.894, b: 0.882, a: 1.0 };

    /// Moccasin color.
    pub const MOCCASIN: Color = Color { r: 1.0, g: 0.894, b: 0.710, a: 1.0 };

    /// Navajo white color.
    pub const NAVAJO_WHITE: Color = Color { r: 1.0, g: 0.871, b: 0.678, a: 1.0 };

    /// Navy blue color.
    pub const NAVY_BLUE: Color = Color { r: 0.0, g: 0.0, b: 0.502, a: 1.0 };

    /// Old lace color.
    pub const OLD_LACE: Color = Color { r: 0.992, g: 0.961, b: 0.902, a: 1.0 };

    /// Olive color.
    pub const OLIVE: Color = Color { r: 0.502, g: 0.502, b: 0.0, a: 1.0 };

    /// Olive drab color.
    pub const OLIVE_DRAB: Color = Color { r: 0.420, g: 0.557, b: 0.137, a: 1.0 };

    /// Orange color.
    pub const ORANGE: Color = Color { r: 1.0, g: 0.647, b: 0.0, a: 1.0 };

    /// Orange red color.
    pub const ORANGE_RED: Color = Color { r: 1.0, g: 0.271, b: 0.0, a: 1.0 };

    /// Orchid color.
    pub const ORCHID: Color = Color { r: 0.855, g: 0.439, b: 0.839, a: 1.0 };

    /// Pale goldenrod color.
    pub const PALE_GOLDENROD: Color = Color { r: 0.933, g: 0.910, b: 0.667, a: 1.0 };

    /// Pale green color.
    pub const PALE_GREEN: Color = Color { r: 0.596, g: 0.984, b: 0.596, a: 1.0 };

    /// Pale turquoise color.
    pub const PALE_TURQUOISE: Color = Color { r: 0.686, g: 0.933, b: 0.933, a: 1.0 };

    /// Pale violet red color.
    pub const PALE_VIOLET_RED: Color = Color { r: 0.859, g: 0.439, b: 0.576, a: 1.0 };

    /// Papaya whip color.
    pub const PAPAYA_WHIP: Color = Color { r: 1.0, g: 0.937, b: 0.835, a: 1.0 };

    /// Peach puff color.
    pub const PEACH_PUFF: Color = Color { r: 1.0, g: 0.855, b: 0.725, a: 1.0 };

    /// Peru color.
    pub const PERU: Color = Color { r: 0.804, g: 0.522, b: 0.247, a: 1.0 };

    /// Pink color.
    pub const PINK: Color = Color { r: 1.0, g: 0.753, b: 0.796, a: 1.0 };

    /// Plum color.
    pub const PLUM: Color = Color { r: 0.867, g: 0.627, b: 0.867, a: 1.0 };

    /// Powder blue color.
    pub const POWDER_BLUE: Color = Color { r: 0.690, g: 0.878, b: 0.902, a: 1.0 };

    /// Purple color.
    pub const PURPLE: Color = Color { r: 0.502, g: 0.0, b: 0.502, a: 1.0 };

    /// Rebecca purple color.
    pub const REBECCA_PURPLE: Color = Color { r: 0.400, g: 0.200, b: 0.600, a: 1.0 };

    /// Red color.
    pub const RED: Color = Color { r: 1.0, g: 0.0, b: 0.0, a: 1.0 };

    /// Rosy brown color.
    pub const ROSY_BROWN: Color = Color { r: 0.737, g: 0.561, b: 0.561, a: 1.0 };

    /// Royal blue color.
    pub const ROYAL_BLUE: Color = Color { r: 0.255, g: 0.412, b: 0.882, a: 1.0 };

    /// Saddle brown color.
    pub const SADDLE_BROWN: Color = Color { r: 0.545, g: 0.271, b: 0.075, a: 1.0 };

    /// Salmon color.
    pub const SALMON: Color = Color { r: 0.980, g: 0.502, b: 0.447, a: 1.0 };

    /// Sandy brown color.
    pub const SANDY_BROWN: Color = Color { r: 0.957, g: 0.643, b: 0.376, a: 1.0 };

    /// Sea green color.
    pub const SEA_GREEN: Color = Color { r: 0.180, g: 0.545, b: 0.341, a: 1.0 };

    /// Seashell color.
    pub const SEASHELL: Color = Color { r: 1.0, g: 0.961, b: 0.933, a: 1.0 };

    /// Sienna color.
    pub const SIENNA: Color = Color { r: 0.627, g: 0.322, b: 0.176, a: 1.0 };

    /// Silver color.
    pub const SILVER: Color = Color { r: 0.753, g: 0.753, b: 0.753, a: 1.0 };

    /// Sky blue color.
    pub const SKY_BLUE: Color = Color { r: 0.529, g: 0.808, b: 0.922, a: 1.0 };

    /// Slate blue color.
    pub const SLATE_BLUE: Color = Color { r: 0.416, g: 0.353, b: 0.804, a: 1.0 };

    /// Slate gray color.
    pub const SLATE_GRAY: Color = Color { r: 0.439, g: 0.502, b: 0.565, a: 1.0 };

    /// Snow color.
    pub const SNOW: Color = Color { r: 1.0, g: 0.980, b: 0.980, a: 1.0 };

    /// Spring green color.
    pub const SPRING_GREEN: Color = Color { r: 0.0, g: 1.0, b: 0.498, a: 1.0 };

    /// Steel blue color.
    pub const STEEL_BLUE: Color = Color { r: 0.275, g: 0.510, b: 0.706, a: 1.0 };

    /// Tan color.
    pub const TAN: Color = Color { r: 0.824, g: 0.706, b: 0.549, a: 1.0 };

    /// Teal color.
    pub const TEAL: Color = Color { r: 0.0, g: 0.502, b: 0.502, a: 1.0 };

    /// Thistle color.
    pub const THISTLE: Color = Color { r: 0.847, g: 0.749, b: 0.847, a: 1.0 };

    /// Tomato color.
    pub const TOMATO: Color = Color { r: 1.0, g: 0.388, b: 0.278, a: 1.0 };

    /// Transparent color (white with zero alpha).
    pub const TRANSPARENT: Color = Color { r: 1.0, g: 1.0, b: 1.0, a: 0.0 };

    /// Turquoise color.
    pub const TURQUOISE: Color = Color { r: 0.251, g: 0.878, b: 0.816, a: 1.0 };

    /// Violet color.
    pub const VIOLET: Color = Color { r: 0.933, g: 0.510, b: 0.933, a: 1.0 };

    /// Web gray color.
    pub const WEB_GRAY: Color = Color { r: 0.502, g: 0.502, b: 0.502, a: 1.0 };

    /// Web green color.
    pub const WEB_GREEN: Color = Color { r: 0.0, g: 0.502, b: 0.0, a: 1.0 };

    /// Web maroon color.
    pub const WEB_MAROON: Color = Color { r: 0.502, g: 0.0, b: 0.0, a: 1.0 };

    /// Web purple color.
    pub const WEB_PURPLE: Color = Color { r: 0.502, g: 0.0, b: 0.502, a: 1.0 };

    /// Wheat color.
    pub const WHEAT: Color = Color { r: 0.961, g: 0.871, b: 0.702, a: 1.0 };

    /// White color.
    pub const WHITE: Color = Color { r: 1.0, g: 1.0, b: 1.0, a: 1.0 };

    /// White smoke color.
    pub const WHITE_SMOKE: Color = Color { r: 0.961, g: 0.961, b: 0.961, a: 1.0 };

    /// Yellow color.
    pub const YELLOW: Color = Color { r: 1.0, g: 1.0, b: 0.0, a: 1.0 };

    /// Yellow green color.
    pub const YELLOW_GREEN: Color = Color { r: 0.604, g: 0.804, b: 0.196, a: 1.0 };

    /// Constructs a new Color from r, g, b, and a.
    #[inline]
    pub const fn new(r: f32, g: f32, b: f32, a: f32) -> Self {
        Self { r, g, b, a }
    }

    /// Constructs a new Color from r, g, and b. Alpha is set to 1.0.
    #[inline]
    pub const fn rgb(r: f32, g: f32, b: f32) -> Self {
        Self { r, g, b, a: 1.0 }
    }

    /// Creates a Color from an HSV profile. The hue (h), saturation (s), and value (v) are typically between 0.0 and 1.0.
    #[inline]
    pub fn from_hsv(h: f32, s: f32, v: f32, a: f32) -> Self {
        let c = v * s;
        let x = c * (1.0 - ((h * 6.0) % 2.0 - 1.0).abs());
        let m = v - c;

        let (r, g, b) = if h < 1.0 / 6.0 {
            (c, x, 0.0)
        } else if h < 2.0 / 6.0 {
            (x, c, 0.0)
        } else if h < 3.0 / 6.0 {
            (0.0, c, x)
        } else if h < 4.0 / 6.0 {
            (0.0, x, c)
        } else if h < 5.0 / 6.0 {
            (x, 0.0, c)
        } else {
            (c, 0.0, x)
        };

        Self {
            r: r + m,
            g: g + m,
            b: b + m,
            a,
        }
    }

    /// Creates a Color from an HSL profile. The hue (h), saturation (s), and lightness (l) are typically between 0.0 and 1.0.
    #[inline]
    pub fn from_hsl(h: f32, s: f32, l: f32, a: f32) -> Self {
        let c = (1.0 - (2.0 * l - 1.0).abs()) * s;
        let x = c * (1.0 - ((h * 6.0) % 2.0 - 1.0).abs());
        let m = l - c / 2.0;

        let (r, g, b) = if h < 1.0 / 6.0 {
            (c, x, 0.0)
        } else if h < 2.0 / 6.0 {
            (x, c, 0.0)
        } else if h < 3.0 / 6.0 {
            (0.0, c, x)
        } else if h < 4.0 / 6.0 {
            (0.0, x, c)
        } else if h < 5.0 / 6.0 {
            (x, 0.0, c)
        } else {
            (c, 0.0, x)
        };

        Self {
            r: r + m,
            g: g + m,
            b: b + m,
            a,
        }
    }

    /// Creates a Color from the given string, which can be either an HTML color code or a named color.
    /// Returns Color::TRANSPARENT if the string is not a valid color.
    #[inline]
    pub fn from_string(color: &str) -> Self {
        // This is a simplified implementation - a full implementation would parse HTML color codes
        match color.to_lowercase().as_str() {
            "red" => Self::RED,
            "green" => Self::GREEN,
            "blue" => Self::BLUE,
            "white" => Self::WHITE,
            "black" => Self::BLACK,
            "yellow" => Self::YELLOW,
            "cyan" => Self::CYAN,
            "magenta" => Self::MAGENTA,
            "transparent" => Self::TRANSPARENT,
            _ => Self::TRANSPARENT,
        }
    }

    /// Returns the result of the linear interpolation between this color and `to` by amount `weight`.
    /// `weight` is on the range of 0.0 to 1.0, representing the amount of interpolation.
    #[inline]
    pub fn lerp(self, to: Color, weight: f32) -> Self {
        Self {
            r: self.r + (to.r - self.r) * weight,
            g: self.g + (to.g - self.g) * weight,
            b: self.b + (to.b - self.b) * weight,
            a: self.a + (to.a - self.a) * weight,
        }
    }

    /// Returns a new color resulting from making this color darker by the specified amount (ratio from 0.0 to 1.0).
    /// See also `lightened()` to do the opposite.
    #[inline]
    pub fn darkened(self, amount: f32) -> Self {
        Self {
            r: self.r * (1.0 - amount),
            g: self.g * (1.0 - amount),
            b: self.b * (1.0 - amount),
            a: self.a,
        }
    }

    /// Returns a new color resulting from making this color lighter by the specified amount (ratio from 0.0 to 1.0).
    /// See also `darkened()` to do the opposite.
    #[inline]
    pub fn lightened(self, amount: f32) -> Self {
        Self {
            r: self.r + (1.0 - self.r) * amount,
            g: self.g + (1.0 - self.g) * amount,
            b: self.b + (1.0 - self.b) * amount,
            a: self.a,
        }
    }

    /// Returns the inverted color (1.0 - r, 1.0 - g, 1.0 - b, a).
    #[inline]
    pub fn inverted(self) -> Self {
        Self {
            r: 1.0 - self.r,
            g: 1.0 - self.g,
            b: 1.0 - self.b,
            a: self.a,
        }
    }

    /// Returns the color with its alpha component set to the given value.
    #[inline]
    pub fn with_alpha(self, alpha: f32) -> Self {
        Self {
            r: self.r,
            g: self.g,
            b: self.b,
            a: alpha,
        }
    }

    /// Returns the luminance of the color in the [0.0, 1.0] range.
    /// This is useful when determining light or dark color. Colors with luminance smaller than 0.5 can be generally considered dark.
    #[inline]
    pub fn get_luminance(self) -> f32 {
        0.299 * self.r + 0.587 * self.g + 0.114 * self.b
    }

    /// Returns true if this color and `to` are approximately equal, by running `@GlobalScope.is_equal_approx` on each component.
    #[inline]
    pub fn is_equal_approx(self, to: Color) -> bool {
        (self.r - to.r).abs() < f32::EPSILON
            && (self.g - to.g).abs() < f32::EPSILON
            && (self.b - to.b).abs() < f32::EPSILON
            && (self.a - to.a).abs() < f32::EPSILON
    }

    /// Returns the color converted to a 32-bit integer in RGBA8 format (each byte represents a component of the RGBA profile).
    /// RGBA8 is more compact than the default, but it loses precision.
    #[inline]
    pub fn to_rgba32(self) -> u32 {
        let r = (self.r.clamp(0.0, 1.0) * 255.0) as u32;
        let g = (self.g.clamp(0.0, 1.0) * 255.0) as u32;
        let b = (self.b.clamp(0.0, 1.0) * 255.0) as u32;
        let a = (self.a.clamp(0.0, 1.0) * 255.0) as u32;
        (r << 24) | (g << 16) | (b << 8) | a
    }

    /// Returns the color converted to a 32-bit integer in ARGB8 format (each byte represents a component of the ARGB profile).
    /// ARGB8 is more compact than the default, but it loses precision.
    #[inline]
    pub fn to_argb32(self) -> u32 {
        let r = (self.r.clamp(0.0, 1.0) * 255.0) as u32;
        let g = (self.g.clamp(0.0, 1.0) * 255.0) as u32;
        let b = (self.b.clamp(0.0, 1.0) * 255.0) as u32;
        let a = (self.a.clamp(0.0, 1.0) * 255.0) as u32;
        (a << 24) | (r << 16) | (g << 8) | b
    }

    /// Constructs a color from a 32-bit integer in RGBA8 format (each byte represents a component of the RGBA profile).
    #[inline]
    pub fn from_rgba32(rgba: u32) -> Self {
        Self {
            r: ((rgba >> 24) & 0xFF) as f32 / 255.0,
            g: ((rgba >> 16) & 0xFF) as f32 / 255.0,
            b: ((rgba >> 8) & 0xFF) as f32 / 255.0,
            a: (rgba & 0xFF) as f32 / 255.0,
        }
    }

    /// Constructs a color from a 32-bit integer in ARGB8 format (each byte represents a component of the ARGB profile).
    #[inline]
    pub fn from_argb32(argb: u32) -> Self {
        Self {
            a: ((argb >> 24) & 0xFF) as f32 / 255.0,
            r: ((argb >> 16) & 0xFF) as f32 / 255.0,
            g: ((argb >> 8) & 0xFF) as f32 / 255.0,
            b: (argb & 0xFF) as f32 / 255.0,
        }
    }
}

// Operator implementations for Color
impl std::ops::Add for Color {
    type Output = Self;

    #[inline]
    fn add(self, rhs: Self) -> Self::Output {
        Self {
            r: self.r + rhs.r,
            g: self.g + rhs.g,
            b: self.b + rhs.b,
            a: self.a + rhs.a,
        }
    }
}

impl std::ops::AddAssign for Color {
    #[inline]
    fn add_assign(&mut self, rhs: Self) {
        self.r += rhs.r;
        self.g += rhs.g;
        self.b += rhs.b;
        self.a += rhs.a;
    }
}

impl std::ops::Sub for Color {
    type Output = Self;

    #[inline]
    fn sub(self, rhs: Self) -> Self::Output {
        Self {
            r: self.r - rhs.r,
            g: self.g - rhs.g,
            b: self.b - rhs.b,
            a: self.a - rhs.a,
        }
    }
}

impl std::ops::SubAssign for Color {
    #[inline]
    fn sub_assign(&mut self, rhs: Self) {
        self.r -= rhs.r;
        self.g -= rhs.g;
        self.b -= rhs.b;
        self.a -= rhs.a;
    }
}

impl std::ops::Mul<Color> for Color {
    type Output = Self;

    #[inline]
    fn mul(self, rhs: Color) -> Self::Output {
        Self {
            r: self.r * rhs.r,
            g: self.g * rhs.g,
            b: self.b * rhs.b,
            a: self.a * rhs.a,
        }
    }
}

impl std::ops::Mul<f32> for Color {
    type Output = Self;

    #[inline]
    fn mul(self, rhs: f32) -> Self::Output {
        Self {
            r: self.r * rhs,
            g: self.g * rhs,
            b: self.b * rhs,
            a: self.a * rhs,
        }
    }
}

impl std::ops::Mul<Color> for f32 {
    type Output = Color;

    #[inline]
    fn mul(self, rhs: Color) -> Self::Output {
        Color {
            r: self * rhs.r,
            g: self * rhs.g,
            b: self * rhs.b,
            a: self * rhs.a,
        }
    }
}

impl std::ops::MulAssign<Color> for Color {
    #[inline]
    fn mul_assign(&mut self, rhs: Color) {
        self.r *= rhs.r;
        self.g *= rhs.g;
        self.b *= rhs.b;
        self.a *= rhs.a;
    }
}

impl std::ops::MulAssign<f32> for Color {
    #[inline]
    fn mul_assign(&mut self, rhs: f32) {
        self.r *= rhs;
        self.g *= rhs;
        self.b *= rhs;
        self.a *= rhs;
    }
}

impl std::ops::Div<Color> for Color {
    type Output = Self;

    #[inline]
    fn div(self, rhs: Color) -> Self::Output {
        Self {
            r: self.r / rhs.r,
            g: self.g / rhs.g,
            b: self.b / rhs.b,
            a: self.a / rhs.a,
        }
    }
}

impl std::ops::Div<f32> for Color {
    type Output = Self;

    #[inline]
    fn div(self, rhs: f32) -> Self::Output {
        Self {
            r: self.r / rhs,
            g: self.g / rhs,
            b: self.b / rhs,
            a: self.a / rhs,
        }
    }
}

impl std::ops::DivAssign<Color> for Color {
    #[inline]
    fn div_assign(&mut self, rhs: Color) {
        self.r /= rhs.r;
        self.g /= rhs.g;
        self.b /= rhs.b;
        self.a /= rhs.a;
    }
}

impl std::ops::DivAssign<f32> for Color {
    #[inline]
    fn div_assign(&mut self, rhs: f32) {
        self.r /= rhs;
        self.g /= rhs;
        self.b /= rhs;
        self.a /= rhs;
    }
}

impl std::ops::Index<usize> for Color {
    type Output = f32;

    #[inline]
    fn index(&self, index: usize) -> &Self::Output {
        match index {
            0 => &self.r,
            1 => &self.g,
            2 => &self.b,
            3 => &self.a,
            _ => panic!("Color index out of bounds: {}", index),
        }
    }
}

impl std::ops::IndexMut<usize> for Color {
    #[inline]
    fn index_mut(&mut self, index: usize) -> &mut Self::Output {
        match index {
            0 => &mut self.r,
            1 => &mut self.g,
            2 => &mut self.b,
            3 => &mut self.a,
            _ => panic!("Color index out of bounds: {}", index),
        }
    }
}
