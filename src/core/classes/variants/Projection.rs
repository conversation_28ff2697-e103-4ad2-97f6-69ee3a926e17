pub struct Projection {
    matrix: [[f32; 4]; 4],
}

impl Projection {
    pub fn new(matrix: [[f32; 4]; 4]) -> Self {
        Self { matrix }
    }

    pub fn identity() -> Self {
        Self {
            matrix: [
                [1.0, 0.0, 0.0, 0.0],
                [0.0, 1.0, 0.0, 0.0],
                [0.0, 0.0, 1.0, 0.0],
                [0.0, 0.0, 0.0, 1.0],
            ]
        }
    }
}
