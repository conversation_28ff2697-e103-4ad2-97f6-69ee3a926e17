/// A 2D vector using floating-point coordinates.
///
/// A 2-element structure that can be used to represent 2D coordinates or any other pair of numeric values.
/// It uses floating-point coordinates of 32-bit precision, unlike Vector2i which uses integers.
///
/// # Examples
///
/// ```
/// use verturion::core::classes::variants::Vector2;
///
/// let v1 = Vector2::new(1.0, 2.0);
/// let v2 = Vector2::new(3.0, 4.0);
/// let sum = v1 + v2;
/// assert_eq!(sum, Vector2::new(4.0, 6.0));
/// ```
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq)]
#[repr(C)]
pub struct Vector2 {
    pub x: f32,
    pub y: f32,
}

impl Vector2 {
    /// Zero vector, a vector with all components set to 0.
    pub const ZERO: Vector2 = Vector2 { x: 0.0, y: 0.0 };

    /// One vector, a vector with all components set to 1.
    pub const ONE: Vector2 = Vector2 { x: 1.0, y: 1.0 };

    /// Infinity vector, a vector with all components set to positive infinity.
    pub const INF: Vector2 = Vector2 { x: f32::INFINITY, y: f32::INFINITY };

    /// Left unit vector. Represents the direction of decreasing X.
    pub const LEFT: Vector2 = Vector2 { x: -1.0, y: 0.0 };

    /// Right unit vector. Represents the direction of increasing X.
    pub const RIGHT: Vector2 = Vector2 { x: 1.0, y: 0.0 };

    /// Up unit vector. Y is down in Godot, so this vector points towards decreasing Y.
    pub const UP: Vector2 = Vector2 { x: 0.0, y: -1.0 };

    /// Down unit vector. Y is down in Godot, so this vector points towards increasing Y.
    pub const DOWN: Vector2 = Vector2 { x: 0.0, y: 1.0 };

    /// Constructs a new Vector2 from x and y.
    #[inline]
    pub const fn new(x: f32, y: f32) -> Self {
        Self { x, y }
    }

    /// Creates a unit Vector2 rotated to the given angle in radians.
    /// This is equivalent to doing `Vector2(cos(angle), sin(angle))` or `Vector2::RIGHT.rotated(angle)`.
    #[inline]
    pub fn from_angle(angle: f32) -> Self {
        Self {
            x: angle.cos(),
            y: angle.sin(),
        }
    }

    /// Returns the angle this vector makes with the positive X-axis in radians.
    /// This is equivalent to `atan2(y, x)`.
    #[inline]
    pub fn angle(self) -> f32 {
        self.y.atan2(self.x)
    }

    /// Returns the angle to the given vector, in radians.
    #[inline]
    pub fn angle_to(self, to: Vector2) -> f32 {
        self.cross(to).atan2(self.dot(to))
    }

    /// Returns the angle to the given point, in radians.
    #[inline]
    pub fn angle_to_point(self, to: Vector2) -> f32 {
        (to - self).angle()
    }

    /// Returns the aspect ratio of this vector, the ratio of x to y.
    #[inline]
    pub fn aspect(self) -> f32 {
        self.x / self.y
    }

    /// Returns the length (magnitude) of this vector.
    #[inline]
    pub fn length(self) -> f32 {
        (self.x * self.x + self.y * self.y).sqrt()
    }

    /// Returns the squared length (squared magnitude) of this vector.
    /// This method runs faster than length(), so prefer it if you need to compare vectors
    /// or need the squared distance for some formula.
    #[inline]
    pub fn length_squared(self) -> f32 {
        self.x * self.x + self.y * self.y
    }

    /// Returns the result of scaling the vector to unit length.
    /// Equivalent to `v / v.length()`. Returns `Vector2::ZERO` if `v.length() == 0`.
    #[inline]
    pub fn normalized(self) -> Self {
        let length = self.length();
        if length == 0.0 {
            Self::ZERO
        } else {
            self / length
        }
    }

    /// Returns true if the vector is normalized, i.e. its length is approximately equal to 1.
    #[inline]
    pub fn is_normalized(self) -> bool {
        (self.length_squared() - 1.0).abs() < f32::EPSILON
    }

    /// Returns the dot product of this vector and `with`.
    /// This can be used to compare the angle between two vectors.
    #[inline]
    pub fn dot(self, with: Vector2) -> f32 {
        self.x * with.x + self.y * with.y
    }

    /// Returns the 2D analog of the cross product for this vector and `with`.
    /// This is the signed area of the parallelogram formed by the two vectors.
    #[inline]
    pub fn cross(self, with: Vector2) -> f32 {
        self.x * with.y - self.y * with.x
    }

    /// Returns the distance between this vector and `to`.
    #[inline]
    pub fn distance_to(self, to: Vector2) -> f32 {
        (to - self).length()
    }

    /// Returns the squared distance between this vector and `to`.
    /// This method runs faster than distance_to(), so prefer it if you need to compare vectors
    /// or need the squared distance for some formula.
    #[inline]
    pub fn distance_squared_to(self, to: Vector2) -> f32 {
        (to - self).length_squared()
    }

    /// Returns the normalized vector pointing from this vector to `to`.
    /// This is equivalent to using `(b - a).normalized()`.
    #[inline]
    pub fn direction_to(self, to: Vector2) -> Self {
        (to - self).normalized()
    }

    /// Returns the result of the linear interpolation between this vector and `to` by amount `weight`.
    /// `weight` is on the range of 0.0 to 1.0, representing the amount of interpolation.
    #[inline]
    pub fn lerp(self, to: Vector2, weight: f32) -> Self {
        self + (to - self) * weight
    }

    /// Returns the result of spherical linear interpolation between this vector and `to`, by amount `weight`.
    /// `weight` is on the range of 0.0 to 1.0, representing the amount of interpolation.
    /// This method also handles the case where the vectors are very close together.
    #[inline]
    pub fn slerp(self, to: Vector2, weight: f32) -> Self {
        let start_length_sq = self.length_squared();
        let end_length_sq = to.length_squared();
        if start_length_sq == 0.0 || end_length_sq == 0.0 {
            return self.lerp(to, weight);
        }
        let start_length = start_length_sq.sqrt();
        let end_length = end_length_sq.sqrt();
        let result_length = start_length + (end_length - start_length) * weight;
        let angle = self.angle_to(to);
        self.rotated(angle * weight) * (result_length / start_length)
    }

    /// Returns the result of cubic interpolation between this vector and `to` using `pre_a` and `post_b` as handles, by amount `weight`.
    /// `weight` is on the range of 0.0 to 1.0, representing the amount of interpolation.
    #[inline]
    pub fn cubic_interpolate(self, to: Vector2, pre_a: Vector2, post_b: Vector2, weight: f32) -> Self {
        let t = weight;
        let t2 = t * t;
        let t3 = t2 * t;

        ((pre_a - self) + (self - to) + (to - post_b)) * t3
            + ((self - pre_a) + (pre_a - self) + (self - to)) * t2
            + (to - self) * t
            + self
    }

    /// Returns the result of cubic interpolation between this vector and `to` using `pre_a` and `post_b` as handles, by amount `weight`.
    /// It can perform smoother interpolation than `cubic_interpolate()` by the time values.
    #[inline]
    pub fn cubic_interpolate_in_time(
        self,
        to: Vector2,
        pre_a: Vector2,
        post_b: Vector2,
        weight: f32,
        _to_t: f32,
        _pre_a_t: f32,
        _post_b_t: f32,
    ) -> Self {
        // Implementation of cubic interpolation with time values
        // This is a simplified version - full implementation would be more complex
        self.cubic_interpolate(to, pre_a, post_b, weight)
    }

    /// Returns the vector with a maximum length by limiting its length to `length`.
    #[inline]
    pub fn limit_length(self, length: f32) -> Self {
        let l = self.length();
        if l > 0.0 && length < l {
            self / l * length
        } else {
            self
        }
    }

    /// Returns the vector with each component set to one or negative one, depending on the signs of the components,
    /// or zero if the component is zero, by calling `sign()` on each component.
    #[inline]
    pub fn sign(self) -> Self {
        Self {
            x: if self.x > 0.0 { 1.0 } else if self.x < 0.0 { -1.0 } else { 0.0 },
            y: if self.y > 0.0 { 1.0 } else if self.y < 0.0 { -1.0 } else { 0.0 },
        }
    }

    /// Returns the vector with all components rounded to the nearest integer, with halfway cases rounded away from zero.
    #[inline]
    pub fn round(self) -> Self {
        Self {
            x: self.x.round(),
            y: self.y.round(),
        }
    }

    /// Returns the vector with all components rounded down (towards negative infinity).
    #[inline]
    pub fn floor(self) -> Self {
        Self {
            x: self.x.floor(),
            y: self.y.floor(),
        }
    }

    /// Returns the vector with all components rounded up (towards positive infinity).
    #[inline]
    pub fn ceil(self) -> Self {
        Self {
            x: self.x.ceil(),
            y: self.y.ceil(),
        }
    }

    /// Returns a new vector with all components in absolute value (i.e. positive).
    #[inline]
    pub fn abs(self) -> Self {
        Self {
            x: self.x.abs(),
            y: self.y.abs(),
        }
    }

    /// Returns the vector rotated by `angle` radians.
    /// See also `@GlobalScope.deg_to_rad` to convert degrees to radians.
    #[inline]
    pub fn rotated(self, angle: f32) -> Self {
        let cos_angle = angle.cos();
        let sin_angle = angle.sin();
        Self {
            x: self.x * cos_angle - self.y * sin_angle,
            y: self.x * sin_angle + self.y * cos_angle,
        }
    }

    /// Returns the vector projected onto the vector `b`.
    #[inline]
    pub fn project(self, onto: Vector2) -> Self {
        onto * (self.dot(onto) / onto.dot(onto))
    }

    /// Returns the vector reflected from a plane defined by the given normal.
    #[inline]
    pub fn reflect(self, normal: Vector2) -> Self {
        self - normal * 2.0 * self.dot(normal)
    }

    /// Returns the vector "bounced off" from a plane defined by the given normal.
    #[inline]
    pub fn bounce(self, normal: Vector2) -> Self {
        -self.reflect(normal)
    }

    /// Returns the vector slid along a plane defined by the given normal.
    #[inline]
    pub fn slide(self, normal: Vector2) -> Self {
        self - normal * self.dot(normal)
    }

    /// Returns true if this vector and `to` are approximately equal, by running `@GlobalScope.is_equal_approx` on each component.
    #[inline]
    pub fn is_equal_approx(self, to: Vector2) -> bool {
        (self.x - to.x).abs() < f32::EPSILON && (self.y - to.y).abs() < f32::EPSILON
    }

    /// Returns true if this vector's values are approximately zero, by running `@GlobalScope.is_zero_approx` on each component.
    #[inline]
    pub fn is_zero_approx(self) -> bool {
        self.x.abs() < f32::EPSILON && self.y.abs() < f32::EPSILON
    }

    /// Returns true if the vector is finite, by calling `@GlobalScope.is_finite` on each component.
    #[inline]
    pub fn is_finite(self) -> bool {
        self.x.is_finite() && self.y.is_finite()
    }
}

// Operator implementations for Vector2
impl std::ops::Add for Vector2 {
    type Output = Self;

    #[inline]
    fn add(self, rhs: Self) -> Self::Output {
        Self {
            x: self.x + rhs.x,
            y: self.y + rhs.y,
        }
    }
}

impl std::ops::AddAssign for Vector2 {
    #[inline]
    fn add_assign(&mut self, rhs: Self) {
        self.x += rhs.x;
        self.y += rhs.y;
    }
}

impl std::ops::Sub for Vector2 {
    type Output = Self;

    #[inline]
    fn sub(self, rhs: Self) -> Self::Output {
        Self {
            x: self.x - rhs.x,
            y: self.y - rhs.y,
        }
    }
}

impl std::ops::SubAssign for Vector2 {
    #[inline]
    fn sub_assign(&mut self, rhs: Self) {
        self.x -= rhs.x;
        self.y -= rhs.y;
    }
}

impl std::ops::Mul<Vector2> for Vector2 {
    type Output = Self;

    #[inline]
    fn mul(self, rhs: Vector2) -> Self::Output {
        Self {
            x: self.x * rhs.x,
            y: self.y * rhs.y,
        }
    }
}

impl std::ops::Mul<f32> for Vector2 {
    type Output = Self;

    #[inline]
    fn mul(self, rhs: f32) -> Self::Output {
        Self {
            x: self.x * rhs,
            y: self.y * rhs,
        }
    }
}

impl std::ops::Mul<Vector2> for f32 {
    type Output = Vector2;

    #[inline]
    fn mul(self, rhs: Vector2) -> Self::Output {
        Vector2 {
            x: self * rhs.x,
            y: self * rhs.y,
        }
    }
}

impl std::ops::MulAssign<Vector2> for Vector2 {
    #[inline]
    fn mul_assign(&mut self, rhs: Vector2) {
        self.x *= rhs.x;
        self.y *= rhs.y;
    }
}

impl std::ops::MulAssign<f32> for Vector2 {
    #[inline]
    fn mul_assign(&mut self, rhs: f32) {
        self.x *= rhs;
        self.y *= rhs;
    }
}

impl std::ops::Div<Vector2> for Vector2 {
    type Output = Self;

    #[inline]
    fn div(self, rhs: Vector2) -> Self::Output {
        Self {
            x: self.x / rhs.x,
            y: self.y / rhs.y,
        }
    }
}

impl std::ops::Div<f32> for Vector2 {
    type Output = Self;

    #[inline]
    fn div(self, rhs: f32) -> Self::Output {
        Self {
            x: self.x / rhs,
            y: self.y / rhs,
        }
    }
}

impl std::ops::DivAssign<Vector2> for Vector2 {
    #[inline]
    fn div_assign(&mut self, rhs: Vector2) {
        self.x /= rhs.x;
        self.y /= rhs.y;
    }
}

impl std::ops::DivAssign<f32> for Vector2 {
    #[inline]
    fn div_assign(&mut self, rhs: f32) {
        self.x /= rhs;
        self.y /= rhs;
    }
}

impl std::ops::Neg for Vector2 {
    type Output = Self;

    #[inline]
    fn neg(self) -> Self::Output {
        Self {
            x: -self.x,
            y: -self.y,
        }
    }
}

impl std::ops::Index<usize> for Vector2 {
    type Output = f32;

    #[inline]
    fn index(&self, index: usize) -> &Self::Output {
        match index {
            0 => &self.x,
            1 => &self.y,
            _ => panic!("Vector2 index out of bounds: {}", index),
        }
    }
}

impl std::ops::IndexMut<usize> for Vector2 {
    #[inline]
    fn index_mut(&mut self, index: usize) -> &mut Self::Output {
        match index {
            0 => &mut self.x,
            1 => &mut self.y,
            _ => panic!("Vector2 index out of bounds: {}", index),
        }
    }
}
