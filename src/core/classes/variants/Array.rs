use super::Variant::Variant;

/// A generic array that can hold any Variant type.
///
/// Array is <PERSON><PERSON>'s generic container for a sequence of Variant values.
/// It supports dynamic resizing, type checking, and various utility methods
/// for manipulation and querying.
///
/// # Examples
///
/// ```
/// use verturion::core::classes::variants::Array;
/// use verturion::core::classes::variants::Variant::Variant;
///
/// let mut array = Array::new();
/// array.push_back(Variant::new_int(42));
/// array.push_back(Variant::new_string("hello".to_string()));
///
/// assert_eq!(array.size(), 2);
/// assert!(!array.is_empty());
/// ```
pub struct Array {
    /// The internal storage for array elements.
    elements: Vec<Variant>,
    /// Whether this array is read-only.
    read_only: bool,
    /// Type information for typed arrays.
    type_info: Option<TypeInfo>,
}

/// Type information for typed arrays.
#[derive(Debu<PERSON>, <PERSON>lone, PartialEq)]
pub struct TypeInfo {
    /// The variant type this array is restricted to.
    pub variant_type: VariantType,
    /// The class name for object types.
    pub class_name: Option<String>,
    /// The script associated with this type.
    pub script: Option<String>, // In a real implementation, this would be a Script reference
}

/// Enumeration of Godot variant types.
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub enum VariantType {
    Nil,
    Bool,
    Int,
    Float,
    String,
    Vector2,
    Vector2i,
    Rect2,
    Rect2i,
    Vector3,
    Vector3i,
    Transform2D,
    Vector4,
    Vector4i,
    Plane,
    Quaternion,
    AABB,
    Basis,
    Transform3D,
    Projection,
    Color,
    StringName,
    NodePath,
    RID,
    Object,
    Callable,
    Signal,
    Dictionary,
    Array,
    PackedByteArray,
    PackedInt32Array,
    PackedInt64Array,
    PackedFloat32Array,
    PackedFloat64Array,
    PackedStringArray,
    PackedVector2Array,
    PackedVector3Array,
    PackedColorArray,
    PackedVector4Array,
}

/// Error types for array operations.
#[derive(Debug, Clone, PartialEq)]
pub enum ArrayError {
    /// Index out of bounds.
    IndexOutOfBounds(usize, usize),
    /// Array is read-only.
    ReadOnly,
    /// Type mismatch for typed arrays.
    TypeMismatch(VariantType, VariantType),
    /// Invalid operation.
    InvalidOperation(String),
}

impl std::fmt::Display for ArrayError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ArrayError::IndexOutOfBounds(index, size) => {
                write!(f, "Index {} out of bounds for array of size {}", index, size)
            }
            ArrayError::ReadOnly => write!(f, "Array is read-only"),
            ArrayError::TypeMismatch(expected, actual) => {
                write!(f, "Type mismatch: expected {:?}, got {:?}", expected, actual)
            }
            ArrayError::InvalidOperation(msg) => write!(f, "Invalid operation: {}", msg),
        }
    }
}

impl std::error::Error for ArrayError {}

/// Result type for array operations.
pub type ArrayResult<T> = Result<T, ArrayError>;

impl Array {
    /// Creates a new empty Array.
    pub fn new() -> Self {
        Self {
            elements: Vec::new(),
            read_only: false,
            type_info: None,
        }
    }

    /// Creates a new typed Array that can only contain elements of the specified type.
    pub fn new_typed(variant_type: VariantType) -> Self {
        Self {
            elements: Vec::new(),
            read_only: false,
            type_info: Some(TypeInfo {
                variant_type,
                class_name: None,
                script: None,
            }),
        }
    }

    /// Creates a new Array from a Vec of Variants.
    pub fn from_vec(elements: Vec<Variant>) -> Self {
        Self {
            elements,
            read_only: false,
            type_info: None,
        }
    }

    /// Creates a new Array with the specified capacity.
    pub fn with_capacity(capacity: usize) -> Self {
        Self {
            elements: Vec::with_capacity(capacity),
            read_only: false,
            type_info: None,
        }
    }

    /// Returns the number of elements in the array.
    pub fn size(&self) -> usize {
        self.elements.len()
    }

    /// Returns true if the array is empty.
    pub fn is_empty(&self) -> bool {
        self.elements.is_empty()
    }

    /// Returns true if the array is read-only.
    pub fn is_read_only(&self) -> bool {
        self.read_only
    }

    /// Makes the array read-only.
    pub fn make_read_only(&mut self) {
        self.read_only = true;
    }

    /// Returns true if the array is typed.
    pub fn is_typed(&self) -> bool {
        self.type_info.is_some()
    }

    /// Returns the variant type of a typed array, or None if not typed.
    pub fn get_typed_builtin(&self) -> Option<VariantType> {
        self.type_info.as_ref().map(|info| info.variant_type)
    }

    /// Returns the class name of a typed array, or None if not applicable.
    pub fn get_typed_class_name(&self) -> Option<&str> {
        self.type_info.as_ref()?.class_name.as_deref()
    }

    /// Returns the script of a typed array, or None if not applicable.
    pub fn get_typed_script(&self) -> Option<&str> {
        self.type_info.as_ref()?.script.as_deref()
    }

    /// Checks if this array has the same type as another array.
    pub fn is_same_typed(&self, other: &Array) -> bool {
        self.type_info == other.type_info
    }

    /// Validates that a variant can be added to this array (for typed arrays).
    fn validate_type(&self, _variant: &Variant) -> ArrayResult<()> {
        if let Some(_type_info) = &self.type_info {
            // In a real implementation, we would check the variant's type
            // against the type_info requirements
            Ok(())
        } else {
            Ok(())
        }
    }

    /// Checks if the array is writable.
    fn check_writable(&self) -> ArrayResult<()> {
        if self.read_only {
            Err(ArrayError::ReadOnly)
        } else {
            Ok(())
        }
    }

    /// Gets the element at the specified index.
    pub fn get(&self, index: usize) -> Option<&Variant> {
        self.elements.get(index)
    }

    /// Gets a mutable reference to the element at the specified index.
    pub fn get_mut(&mut self, index: usize) -> Option<&mut Variant> {
        if self.read_only {
            None
        } else {
            self.elements.get_mut(index)
        }
    }

    /// Sets the value at the specified index.
    pub fn set(&mut self, index: usize, value: Variant) -> ArrayResult<()> {
        self.check_writable()?;
        self.validate_type(&value)?;

        if index >= self.elements.len() {
            return Err(ArrayError::IndexOutOfBounds(index, self.elements.len()));
        }

        self.elements[index] = value;
        Ok(())
    }

    /// Appends an element to the end of the array.
    pub fn push_back(&mut self, value: Variant) -> ArrayResult<()> {
        self.check_writable()?;
        self.validate_type(&value)?;
        self.elements.push(value);
        Ok(())
    }

    /// Appends an element to the end of the array (alias for push_back).
    pub fn append(&mut self, value: Variant) -> ArrayResult<()> {
        self.push_back(value)
    }

    /// Adds an element to the beginning of the array.
    pub fn push_front(&mut self, value: Variant) -> ArrayResult<()> {
        self.check_writable()?;
        self.validate_type(&value)?;
        self.elements.insert(0, value);
        Ok(())
    }

    /// Removes and returns the last element of the array.
    pub fn pop_back(&mut self) -> ArrayResult<Option<Variant>> {
        self.check_writable()?;
        Ok(self.elements.pop())
    }

    /// Removes and returns the first element of the array.
    pub fn pop_front(&mut self) -> ArrayResult<Option<Variant>> {
        self.check_writable()?;
        if self.elements.is_empty() {
            Ok(None)
        } else {
            Ok(Some(self.elements.remove(0)))
        }
    }

    /// Removes and returns the element at the specified index.
    pub fn pop_at(&mut self, index: usize) -> ArrayResult<Option<Variant>> {
        self.check_writable()?;
        if index >= self.elements.len() {
            return Err(ArrayError::IndexOutOfBounds(index, self.elements.len()));
        }
        Ok(Some(self.elements.remove(index)))
    }

    /// Inserts an element at the specified position.
    pub fn insert(&mut self, position: usize, value: Variant) -> ArrayResult<()> {
        self.check_writable()?;
        self.validate_type(&value)?;

        if position > self.elements.len() {
            return Err(ArrayError::IndexOutOfBounds(position, self.elements.len()));
        }

        self.elements.insert(position, value);
        Ok(())
    }

    /// Removes the element at the specified position.
    pub fn remove_at(&mut self, position: usize) -> ArrayResult<()> {
        self.check_writable()?;

        if position >= self.elements.len() {
            return Err(ArrayError::IndexOutOfBounds(position, self.elements.len()));
        }

        self.elements.remove(position);
        Ok(())
    }

    /// Removes the first occurrence of the specified value.
    pub fn erase(&mut self, value: &Variant) -> ArrayResult<bool> {
        self.check_writable()?;

        if let Some(index) = self.find(value) {
            self.elements.remove(index);
            Ok(true)
        } else {
            Ok(false)
        }
    }

    /// Finds the first occurrence of the specified value.
    pub fn find(&self, value: &Variant) -> Option<usize> {
        // In a real implementation, we would compare Variants properly
        // For now, this is a placeholder
        for (i, element) in self.elements.iter().enumerate() {
            // This would need proper Variant comparison
            if std::ptr::eq(element, value) {
                return Some(i);
            }
        }
        None
    }

    /// Finds the first occurrence of the specified value starting from the end.
    pub fn rfind(&self, value: &Variant) -> Option<usize> {
        // In a real implementation, we would compare Variants properly
        for (i, element) in self.elements.iter().enumerate().rev() {
            if std::ptr::eq(element, value) {
                return Some(i);
            }
        }
        None
    }

    /// Checks if the array contains the specified value.
    pub fn has(&self, value: &Variant) -> bool {
        self.find(value).is_some()
    }

    /// Returns the number of occurrences of the specified value.
    pub fn count(&self, value: &Variant) -> usize {
        self.elements.iter()
            .filter(|element| std::ptr::eq(*element, value))
            .count()
    }

    /// Resizes the array to the specified size.
    pub fn resize(&mut self, size: usize) -> ArrayResult<()> {
        self.check_writable()?;

        if size < self.elements.len() {
            self.elements.truncate(size);
        } else if size > self.elements.len() {
            let additional = size - self.elements.len();
            for _ in 0..additional {
                // In a real implementation, we would create appropriate default values
                // based on the array's type
                self.elements.push(Variant::new_nil());
            }
        }

        Ok(())
    }

    /// Clears all elements from the array.
    pub fn clear(&mut self) -> ArrayResult<()> {
        self.check_writable()?;
        self.elements.clear();
        Ok(())
    }

    /// Reverses the order of elements in the array.
    pub fn reverse(&mut self) -> ArrayResult<()> {
        self.check_writable()?;
        self.elements.reverse();
        Ok(())
    }

    /// Sorts the array in ascending order.
    pub fn sort(&mut self) -> ArrayResult<()> {
        self.check_writable()?;
        // In a real implementation, we would implement proper Variant comparison
        // self.elements.sort_by(|a, b| a.cmp(b));
        Ok(())
    }

    /// Shuffles the array randomly.
    pub fn shuffle(&mut self) -> ArrayResult<()> {
        self.check_writable()?;
        // In a real implementation, we would use a proper random number generator
        // For now, this is a placeholder
        Ok(())
    }

    /// Returns a slice of the array.
    pub fn slice(&self, begin: usize, end: usize, step: usize) -> Array {
        let mut result = Array::new();

        if step == 0 || begin >= self.elements.len() {
            return result;
        }

        let actual_end = end.min(self.elements.len());
        let mut i = begin;

        while i < actual_end {
            if let Some(_element) = self.elements.get(i) {
                // In a real implementation, we would clone the variant properly
                let _ = result.push_back(Variant::new_nil());
            }
            i += step;
        }

        result
    }

    /// Duplicates the array.
    pub fn duplicate(&self, deep: bool) -> Array {
        let mut result = Array::new();
        result.type_info = self.type_info.clone();

        for _element in &self.elements {
            // In a real implementation, we would properly clone/duplicate variants
            // The 'deep' parameter would control whether nested containers are also duplicated
            let _ = if deep {
                result.push_back(Variant::new_nil()) // Deep clone placeholder
            } else {
                result.push_back(Variant::new_nil()) // Shallow clone placeholder
            };
        }

        result
    }

    /// Appends all elements from another array.
    pub fn append_array(&mut self, other: &Array) -> ArrayResult<()> {
        self.check_writable()?;

        for element in &other.elements {
            self.validate_type(element)?;
            // In a real implementation, we would clone the variant properly
            self.elements.push(Variant::new_nil());
        }

        Ok(())
    }

    /// Returns a random element from the array.
    pub fn pick_random(&self) -> Option<&Variant> {
        if self.elements.is_empty() {
            None
        } else {
            // In a real implementation, we would use a proper random number generator
            // For now, just return the first element
            self.elements.first()
        }
    }

    /// Returns the maximum element in the array.
    pub fn max(&self) -> Option<&Variant> {
        // In a real implementation, we would implement proper Variant comparison
        self.elements.first()
    }

    /// Returns the minimum element in the array.
    pub fn min(&self) -> Option<&Variant> {
        // In a real implementation, we would implement proper Variant comparison
        self.elements.first()
    }

    /// Computes a hash of the array.
    pub fn hash(&self) -> u32 {
        // In a real implementation, we would hash all elements
        self.elements.len() as u32
    }

    /// Returns an iterator over the elements.
    pub fn iter(&self) -> std::slice::Iter<Variant> {
        self.elements.iter()
    }

    /// Returns a mutable iterator over the elements.
    pub fn iter_mut(&mut self) -> ArrayResult<std::slice::IterMut<Variant>> {
        self.check_writable()?;
        Ok(self.elements.iter_mut())
    }

    /// Converts the array to a Vec.
    pub fn to_vec(&self) -> Vec<Variant> {
        // In a real implementation, we would clone variants properly
        let mut result = Vec::with_capacity(self.elements.len());
        for _ in 0..self.elements.len() {
            result.push(Variant::new_nil());
        }
        result
    }

    /// Returns the first element.
    pub fn front(&self) -> Option<&Variant> {
        self.elements.first()
    }

    /// Returns the last element.
    pub fn back(&self) -> Option<&Variant> {
        self.elements.last()
    }

    /// Checks if all elements satisfy a condition.
    pub fn all<F>(&self, mut predicate: F) -> bool
    where
        F: FnMut(&Variant) -> bool,
    {
        self.elements.iter().all(|element| predicate(element))
    }

    /// Checks if any element satisfies a condition.
    pub fn any<F>(&self, mut predicate: F) -> bool
    where
        F: FnMut(&Variant) -> bool,
    {
        self.elements.iter().any(|element| predicate(element))
    }
}

impl Default for Array {
    fn default() -> Self {
        Self::new()
    }
}

impl PartialEq for Array {
    fn eq(&self, other: &Self) -> bool {
        if self.elements.len() != other.elements.len() {
            return false;
        }

        // In a real implementation, we would compare variants properly
        self.elements.len() == other.elements.len()
    }
}

impl Eq for Array {}

/// Index operator for read access.
impl std::ops::Index<usize> for Array {
    type Output = Variant;

    fn index(&self, index: usize) -> &Self::Output {
        &self.elements[index]
    }
}

/// Index operator for write access.
impl std::ops::IndexMut<usize> for Array {
    fn index_mut(&mut self, index: usize) -> &mut Self::Output {
        &mut self.elements[index]
    }
}

/// Concatenation operator.
impl std::ops::Add for Array {
    type Output = Array;

    fn add(self, other: Array) -> Array {
        let mut result = self;
        let _ = result.append_array(&other);
        result
    }
}

/// Concatenation assignment operator.
impl std::ops::AddAssign for Array {
    fn add_assign(&mut self, other: Array) {
        let _ = self.append_array(&other);
    }
}
