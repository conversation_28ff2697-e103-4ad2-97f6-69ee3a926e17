use super::Variant::Variant;
use super::Array::VariantType;
use std::collections::HashMap;

/// A generic dictionary that can hold key-value pairs of any Variant type.
///
/// Dictionary is God<PERSON>'s generic container for key-value pairs of Variant values.
/// It supports dynamic resizing, type checking for typed dictionaries, and various
/// utility methods for manipulation and querying.
///
/// # Examples
///
/// ```
/// use verturion::core::classes::variants::Dictionary;
/// use verturion::core::classes::variants::Variant::Variant;
///
/// let mut dict = Dictionary::new();
/// dict.set(Variant::new_string("name".to_string()), Variant::new_string("Player".to_string()));
/// dict.set(Variant::new_string("level".to_string()), Variant::new_int(42));
///
/// assert_eq!(dict.size(), 2);
/// assert!(!dict.is_empty());
/// ```
pub struct Dictionary {
    /// The internal storage for dictionary entries.
    data: HashMap<DictKey, Variant>,
    /// Whether this dictionary is read-only.
    read_only: bool,
    /// Type information for typed dictionaries.
    key_type_info: Option<TypeInfo>,
    /// Type information for typed dictionary values.
    value_type_info: Option<TypeInfo>,
}

/// A wrapper for dictionary keys that implements Hash and Eq.
/// This is needed because Variant doesn't implement these traits yet.
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct DictKey {
    /// A simplified representation of the key for hashing.
    /// In a real implementation, this would be more sophisticated.
    key_repr: String,
}

impl DictKey {
    pub fn from_variant(_variant: &Variant) -> Self {
        // In a real implementation, this would properly convert variants to hashable keys
        // For now, we'll use a placeholder since Variant doesn't implement Debug
        Self {
            key_repr: "variant_key".to_string(),
        }
    }
}

/// Type information for typed dictionaries.
#[derive(Debug, Clone, PartialEq)]
pub struct TypeInfo {
    /// The variant type this is restricted to.
    pub variant_type: VariantType,
    /// The class name for object types.
    pub class_name: Option<String>,
    /// The script associated with this type.
    pub script: Option<String>,
}

/// Error types for dictionary operations.
#[derive(Debug, Clone, PartialEq)]
pub enum DictionaryError {
    /// Dictionary is read-only.
    ReadOnly,
    /// Type mismatch for typed dictionaries.
    KeyTypeMismatch(VariantType, VariantType),
    /// Type mismatch for typed dictionary values.
    ValueTypeMismatch(VariantType, VariantType),
    /// Invalid operation.
    InvalidOperation(String),
}

impl std::fmt::Display for DictionaryError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            DictionaryError::ReadOnly => write!(f, "Dictionary is read-only"),
            DictionaryError::KeyTypeMismatch(expected, actual) => {
                write!(f, "Key type mismatch: expected {:?}, got {:?}", expected, actual)
            }
            DictionaryError::ValueTypeMismatch(expected, actual) => {
                write!(f, "Value type mismatch: expected {:?}, got {:?}", expected, actual)
            }
            DictionaryError::InvalidOperation(msg) => write!(f, "Invalid operation: {}", msg),
        }
    }
}

impl std::error::Error for DictionaryError {}

/// Result type for dictionary operations.
pub type DictionaryResult<T> = Result<T, DictionaryError>;

impl Dictionary {
    /// Creates a new empty Dictionary.
    pub fn new() -> Self {
        Self {
            data: HashMap::new(),
            read_only: false,
            key_type_info: None,
            value_type_info: None,
        }
    }

    /// Creates a new typed Dictionary with specified key and value types.
    pub fn new_typed(key_type: VariantType, value_type: VariantType) -> Self {
        Self {
            data: HashMap::new(),
            read_only: false,
            key_type_info: Some(TypeInfo {
                variant_type: key_type,
                class_name: None,
                script: None,
            }),
            value_type_info: Some(TypeInfo {
                variant_type: value_type,
                class_name: None,
                script: None,
            }),
        }
    }

    /// Creates a new Dictionary from a HashMap.
    pub fn from_hashmap(data: HashMap<DictKey, Variant>) -> Self {
        Self {
            data,
            read_only: false,
            key_type_info: None,
            value_type_info: None,
        }
    }

    /// Returns the number of entries in the dictionary.
    pub fn size(&self) -> usize {
        self.data.len()
    }

    /// Returns true if the dictionary is empty.
    pub fn is_empty(&self) -> bool {
        self.data.is_empty()
    }

    /// Returns true if the dictionary is read-only.
    pub fn is_read_only(&self) -> bool {
        self.read_only
    }

    /// Makes the dictionary read-only.
    pub fn make_read_only(&mut self) {
        self.read_only = true;
    }

    /// Returns true if the dictionary is typed.
    pub fn is_typed(&self) -> bool {
        self.key_type_info.is_some() || self.value_type_info.is_some()
    }

    /// Returns true if the dictionary's keys are typed.
    pub fn is_typed_key(&self) -> bool {
        self.key_type_info.is_some()
    }

    /// Returns true if the dictionary's values are typed.
    pub fn is_typed_value(&self) -> bool {
        self.value_type_info.is_some()
    }

    /// Returns the variant type of typed keys, or None if not typed.
    pub fn get_typed_key_builtin(&self) -> Option<VariantType> {
        self.key_type_info.as_ref().map(|info| info.variant_type)
    }

    /// Returns the variant type of typed values, or None if not typed.
    pub fn get_typed_value_builtin(&self) -> Option<VariantType> {
        self.value_type_info.as_ref().map(|info| info.variant_type)
    }

    /// Returns the class name of typed keys, or None if not applicable.
    pub fn get_typed_key_class_name(&self) -> Option<&str> {
        self.key_type_info.as_ref()?.class_name.as_deref()
    }

    /// Returns the class name of typed values, or None if not applicable.
    pub fn get_typed_value_class_name(&self) -> Option<&str> {
        self.value_type_info.as_ref()?.class_name.as_deref()
    }

    /// Returns the script of typed keys, or None if not applicable.
    pub fn get_typed_key_script(&self) -> Option<&str> {
        self.key_type_info.as_ref()?.script.as_deref()
    }

    /// Returns the script of typed values, or None if not applicable.
    pub fn get_typed_value_script(&self) -> Option<&str> {
        self.value_type_info.as_ref()?.script.as_deref()
    }

    /// Checks if this dictionary has the same type as another dictionary.
    pub fn is_same_typed(&self, other: &Dictionary) -> bool {
        self.key_type_info == other.key_type_info &&
        self.value_type_info == other.value_type_info
    }

    /// Checks if this dictionary has the same key type as another dictionary.
    pub fn is_same_typed_key(&self, other: &Dictionary) -> bool {
        self.key_type_info == other.key_type_info
    }

    /// Checks if this dictionary has the same value type as another dictionary.
    pub fn is_same_typed_value(&self, other: &Dictionary) -> bool {
        self.value_type_info == other.value_type_info
    }

    /// Validates that a key can be added to this dictionary (for typed dictionaries).
    fn validate_key_type(&self, _key: &Variant) -> DictionaryResult<()> {
        if let Some(_type_info) = &self.key_type_info {
            // In a real implementation, we would check the variant's type
            // against the type_info requirements
            Ok(())
        } else {
            Ok(())
        }
    }

    /// Validates that a value can be added to this dictionary (for typed dictionaries).
    fn validate_value_type(&self, _value: &Variant) -> DictionaryResult<()> {
        if let Some(_type_info) = &self.value_type_info {
            // In a real implementation, we would check the variant's type
            // against the type_info requirements
            Ok(())
        } else {
            Ok(())
        }
    }

    /// Checks if the dictionary is writable.
    fn check_writable(&self) -> DictionaryResult<()> {
        if self.read_only {
            Err(DictionaryError::ReadOnly)
        } else {
            Ok(())
        }
    }

    /// Gets the value for the specified key.
    pub fn get(&self, key: &Variant) -> Option<&Variant> {
        let dict_key = DictKey::from_variant(key);
        self.data.get(&dict_key)
    }

    /// Gets the value for the specified key, or returns a default value.
    pub fn get_or_default(&self, key: &Variant, default: Variant) -> Variant {
        if let Some(_value) = self.get(key) {
            // In a real implementation, we would clone the variant properly
            Variant::new_nil()
        } else {
            default
        }
    }

    /// Gets a value and ensures the key is set.
    pub fn get_or_add(&mut self, key: Variant, default: Variant) -> DictionaryResult<&Variant> {
        self.check_writable()?;
        self.validate_key_type(&key)?;
        self.validate_value_type(&default)?;

        let dict_key = DictKey::from_variant(&key);
        if !self.data.contains_key(&dict_key) {
            self.data.insert(dict_key.clone(), default);
        }

        Ok(self.data.get(&dict_key).unwrap())
    }

    /// Sets the value for the specified key.
    pub fn set(&mut self, key: Variant, value: Variant) -> DictionaryResult<bool> {
        self.check_writable()?;
        self.validate_key_type(&key)?;
        self.validate_value_type(&value)?;

        let dict_key = DictKey::from_variant(&key);
        let existed = self.data.contains_key(&dict_key);
        self.data.insert(dict_key, value);

        Ok(existed)
    }

    /// Checks if the dictionary contains the specified key.
    pub fn has(&self, key: &Variant) -> bool {
        let dict_key = DictKey::from_variant(key);
        self.data.contains_key(&dict_key)
    }

    /// Checks if the dictionary contains all the specified keys.
    pub fn has_all(&self, keys: &[Variant]) -> bool {
        keys.iter().all(|key| self.has(key))
    }

    /// Removes the entry with the specified key.
    pub fn erase(&mut self, key: &Variant) -> DictionaryResult<bool> {
        self.check_writable()?;

        let dict_key = DictKey::from_variant(key);
        Ok(self.data.remove(&dict_key).is_some())
    }

    /// Finds the first key whose value matches the specified value.
    pub fn find_key(&self, _value: &Variant) -> Option<DictKey> {
        // In a real implementation, we would compare variants properly
        for (key, _val) in &self.data {
            // This would need proper Variant comparison
            return Some(key.clone());
        }
        None
    }

    /// Returns an array of all keys in the dictionary.
    pub fn keys(&self) -> Vec<DictKey> {
        self.data.keys().cloned().collect()
    }

    /// Returns an array of all values in the dictionary.
    pub fn values(&self) -> Vec<&Variant> {
        self.data.values().collect()
    }

    /// Clears all entries from the dictionary.
    pub fn clear(&mut self) -> DictionaryResult<()> {
        self.check_writable()?;
        self.data.clear();
        Ok(())
    }

    /// Duplicates the dictionary.
    pub fn duplicate(&self, deep: bool) -> Dictionary {
        let mut result = Dictionary::new();
        result.key_type_info = self.key_type_info.clone();
        result.value_type_info = self.value_type_info.clone();

        for (key, _value) in &self.data {
            // In a real implementation, we would properly clone/duplicate variants
            // The 'deep' parameter would control whether nested containers are also duplicated
            let _ = if deep {
                result.data.insert(key.clone(), Variant::new_nil()) // Deep clone placeholder
            } else {
                result.data.insert(key.clone(), Variant::new_nil()) // Shallow clone placeholder
            };
        }

        result
    }

    /// Merges another dictionary into this one.
    pub fn merge(&mut self, other: &Dictionary, overwrite: bool) -> DictionaryResult<()> {
        self.check_writable()?;

        for (key, value) in &other.data {
            if overwrite || !self.data.contains_key(key) {
                self.validate_key_type(&Variant::new_nil())?; // Placeholder validation
                self.validate_value_type(value)?;
                // In a real implementation, we would clone the variant properly
                self.data.insert(key.clone(), Variant::new_nil());
            }
        }

        Ok(())
    }

    /// Returns a new dictionary that is the result of merging this dictionary with another.
    pub fn merged(&self, other: &Dictionary, overwrite: bool) -> Dictionary {
        let mut result = self.duplicate(false);
        let _ = result.merge(other, overwrite);
        result
    }

    /// Assigns elements of another dictionary into this dictionary.
    pub fn assign(&mut self, other: &Dictionary) -> DictionaryResult<()> {
        self.check_writable()?;

        self.data.clear();
        for (key, value) in &other.data {
            self.validate_key_type(&Variant::new_nil())?; // Placeholder validation
            self.validate_value_type(value)?;
            // In a real implementation, we would clone the variant properly
            self.data.insert(key.clone(), Variant::new_nil());
        }

        Ok(())
    }

    /// Sorts the dictionary in-place by key.
    pub fn sort(&mut self) -> DictionaryResult<()> {
        self.check_writable()?;

        // In a real implementation, we would sort the dictionary by keys
        // For now, this is a placeholder since we can't easily sort HashMap in-place
        Ok(())
    }

    /// Computes a hash of the dictionary.
    pub fn hash(&self) -> u32 {
        // In a real implementation, we would hash all key-value pairs
        self.data.len() as u32
    }

    /// Checks if two dictionaries are recursively equal.
    pub fn recursive_equal(&self, other: &Dictionary, _recursion_count: usize) -> bool {
        if self.data.len() != other.data.len() {
            return false;
        }

        // In a real implementation, we would compare all key-value pairs recursively
        self.data.len() == other.data.len()
    }
}

impl Default for Dictionary {
    fn default() -> Self {
        Self::new()
    }
}

impl PartialEq for Dictionary {
    fn eq(&self, other: &Self) -> bool {
        if self.data.len() != other.data.len() {
            return false;
        }

        // In a real implementation, we would compare all key-value pairs properly
        self.data.len() == other.data.len()
    }
}

impl Eq for Dictionary {}

/// Index operator for read access.
impl std::ops::Index<&Variant> for Dictionary {
    type Output = Variant;

    fn index(&self, key: &Variant) -> &Self::Output {
        let dict_key = DictKey::from_variant(key);
        &self.data[&dict_key]
    }
}

/// Index operator for write access.
impl std::ops::IndexMut<&Variant> for Dictionary {
    fn index_mut(&mut self, key: &Variant) -> &mut Self::Output {
        let dict_key = DictKey::from_variant(key);
        self.data.get_mut(&dict_key).expect("Key not found")
    }
}

impl std::fmt::Display for Dictionary {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{{")?;
        let mut first = true;
        for (key, _value) in &self.data {
            if !first {
                write!(f, ", ")?;
            }
            // In a real implementation, we would display the key and value properly
            write!(f, "{:?}: <variant>", key.key_repr)?;
            first = false;
        }
        write!(f, "}}")
    }
}

/// Iterator support for Dictionary.
impl Dictionary {
    /// Returns an iterator over the key-value pairs.
    pub fn iter(&self) -> std::collections::hash_map::Iter<DictKey, Variant> {
        self.data.iter()
    }

    /// Returns a mutable iterator over the key-value pairs.
    pub fn iter_mut(&mut self) -> DictionaryResult<std::collections::hash_map::IterMut<DictKey, Variant>> {
        self.check_writable()?;
        Ok(self.data.iter_mut())
    }

    /// Returns an iterator over the keys.
    pub fn keys_iter(&self) -> std::collections::hash_map::Keys<DictKey, Variant> {
        self.data.keys()
    }

    /// Returns an iterator over the values.
    pub fn values_iter(&self) -> std::collections::hash_map::Values<DictKey, Variant> {
        self.data.values()
    }

    /// Returns a mutable iterator over the values.
    pub fn values_iter_mut(&mut self) -> DictionaryResult<std::collections::hash_map::ValuesMut<DictKey, Variant>> {
        self.check_writable()?;
        Ok(self.data.values_mut())
    }

    /// Converts the dictionary to a HashMap.
    pub fn to_hashmap(&self) -> HashMap<DictKey, Variant> {
        // In a real implementation, we would clone variants properly
        let mut result = HashMap::new();
        for (key, _value) in &self.data {
            result.insert(key.clone(), Variant::new_nil());
        }
        result
    }

    /// Creates a dictionary from key-value pairs.
    pub fn from_pairs(pairs: Vec<(Variant, Variant)>) -> Self {
        let mut dict = Dictionary::new();
        for (key, value) in pairs {
            let _ = dict.set(key, value);
        }
        dict
    }

    /// Retains only the entries specified by the predicate.
    pub fn retain<F>(&mut self, mut predicate: F) -> DictionaryResult<()>
    where
        F: FnMut(&DictKey, &mut Variant) -> bool,
    {
        self.check_writable()?;
        self.data.retain(|key, value| predicate(key, value));
        Ok(())
    }

    /// Returns the capacity of the dictionary.
    pub fn capacity(&self) -> usize {
        self.data.capacity()
    }

    /// Reserves capacity for at least additional more elements.
    pub fn reserve(&mut self, additional: usize) -> DictionaryResult<()> {
        self.check_writable()?;
        self.data.reserve(additional);
        Ok(())
    }

    /// Shrinks the capacity of the dictionary as much as possible.
    pub fn shrink_to_fit(&mut self) -> DictionaryResult<()> {
        self.check_writable()?;
        self.data.shrink_to_fit();
        Ok(())
    }
}
