use super::*;

pub enum Variant {
    Nil,
    AABB(AABB::AABB),
    Array(Array::Array),
    <PERSON><PERSON>(Basis::Basis),
    <PERSON><PERSON>(bool::Bool),
    Callable(Callable::Callable),
    Color(Color::Color),
    Dictionary(Dictionary::Dictionary),
    Float(float::Float),
    Int(int::Int),
    NodePath(NodePath::NodePath),
    Object(Object::Object),
    PackedByteArray(PackedByteArray::PackedByteArray),
    PackedInt32Array(PackedInt32Array::PackedInt32Array),
    PackedInt64Array(PackedInt64Array::PackedInt64Array),
    PackedFloat32Array(PackedFloat32Array::PackedFloat32Array),
    PackedFloat64Array(PackedFloat64Array::PackedFloat64Array),
    PackedVector2Array(PackedVector2Array::PackedVector2Array),
    PackedVector3Array(PackedVector3Array::PackedVector3Array),
    PackedColorArray(PackedColorArray::PackedColorArray),
    PackedStringArray(PackedStringArray::PackedStringArray),
    Plane(Plane::Plane),
    Projection(Projection::Projection),
    Quaternion(Quaternion::Quaternion),
    Rect2(Rect2::Rect2),
    Rect2i(Rect2i::Rect2i),
    RID(RID::RID),
    Signal(Signal::Signal),
    StringName(StringName::StringName),
    String(String::String),
    Transform2D(Transform2D::Transform2D),
    Transform3D(Transform3D::Transform3D),
    Vector2(Vector2::Vector2),
    Vector2i(Vector2i::Vector2i),
    Vector3(Vector3::Vector3),
    Vector3i(Vector3i::Vector3i),
    Vector4(Vector4::Vector4),
    Vector4i(Vector4i::Vector4i),
    
}

impl Variant {
    pub fn new_nil() -> Self {
        Self::Nil
    }
}
