#!/usr/bin/env python3
"""
Script to extract variant types from Godot documentation.
"""

import requests
from bs4 import BeautifulSoup

def extract_variant_types():
    """Extract variant types from Godot documentation."""
    url = "https://docs.godotengine.org/en/stable/classes/index.html"
    response = requests.get(url)
    soup = BeautifulSoup(response.content, 'html.parser')
    
    variant_types = []
    in_variant_section = False
    
    # Look for the variant types section
    for element in soup.find_all(['h1', 'h2', 'li']):
        if element.name in ['h1', 'h2']:
            text = element.get_text().strip().lower()
            if 'variant types' in text:
                in_variant_section = True
                continue
            elif in_variant_section and any(section in text for section in ['globals', 'editor', 'nodes', 'resources', 'other']):
                in_variant_section = False
                break
        
        elif element.name == 'li' and in_variant_section:
            # Look for class links
            link = element.find('a')
            if link and link.get('href', '').startswith('class_'):
                class_name = link.get_text().strip()
                if class_name:
                    variant_types.append(class_name)
    
    return variant_types

def main():
    print("Extracting variant types from Godot documentation...")
    variant_types = extract_variant_types()
    
    print(f"\nFound {len(variant_types)} variant types:")
    for vtype in sorted(variant_types):
        print(f"  - {vtype}")
    
    # Compare with current implementation
    current_variants = [
        'AABB', 'Array', 'Basis', 'Bool', 'Callable', 'Color', 'Dictionary',
        'Float', 'Int', 'NodePath', 'Matrix', 'Object', 'PackedByteArray',
        'PackedColorArray', 'PackedFloat32Array', 'PackedFloat64Array',
        'PackedInt32Array', 'PackedInt64Array', 'PackedStringArray',
        'PackedVector2Array', 'PackedVector3Array', 'PackedVector4Array',
        'Plane', 'Projection', 'Quaternion', 'Rect2', 'Rect2i', 'RID',
        'Signal', 'VString', 'VStringName', 'Transform2D', 'Transform3D',
        'Variant', 'Vector2', 'Vector2i', 'Vector3', 'Vector3i', 'Vector4', 'Vector4i'
    ]
    
    print(f"\nCurrent implementation has {len(current_variants)} types")
    
    # Find differences
    godot_set = set(variant_types)
    current_set = set(current_variants)
    
    missing = godot_set - current_set
    extra = current_set - godot_set
    
    print(f"\nMissing from current implementation: {len(missing)}")
    for item in sorted(missing):
        print(f"  - {item}")
    
    print(f"\nExtra in current implementation: {len(extra)}")
    for item in sorted(extra):
        print(f"  - {item}")
    
    # Check for naming differences
    print(f"\nPossible naming differences:")
    if 'String' in godot_set and 'VString' in current_set:
        print("  - VString should be String")
    if 'StringName' in godot_set and 'VStringName' in current_set:
        print("  - VStringName should be StringName")
    if 'bool' in godot_set and 'Bool' in current_set:
        print("  - Bool should be bool")
    if 'float' in godot_set and 'Float' in current_set:
        print("  - Float should be float")
    if 'int' in godot_set and 'Int' in current_set:
        print("  - Int should be int")

if __name__ == "__main__":
    main()
