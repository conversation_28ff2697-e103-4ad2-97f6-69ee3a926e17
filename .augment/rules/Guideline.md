---
type: "always_apply"
---

Augment AI Software Development Guidelines

Planning & Analysis

Codebase Understanding
- Analyze before acting: Always examine existing code structure, patterns, and conventions before proposing changes
- Map dependencies: Identify all files, modules, and components that may be affected by proposed modifications
- Understand the architecture: Recognize the project's architectural patterns (MVC, modular design, etc.) and maintain consistency
- Review existing implementations: Look for similar functionality already implemented to maintain consistency

Task Decomposition
- Break down complexity: Divide large tasks into smaller, atomic steps that can be implemented and tested independently
- Prioritize changes: Order modifications by dependency requirements and risk level
- Identify prerequisites: Ensure all necessary components exist before implementing dependent features
- Plan rollback strategy: Consider how changes can be safely reverted if issues arise

Requirements Validation
- Clarify ambiguities: Ask specific questions when requirements are unclear or incomplete
- Confirm scope: Verify the extent of changes needed and any constraints
- Validate assumptions: Explicitly state assumptions about functionality or behavior
- Check compatibility: Ensure proposed changes align with existing system capabilities

File Management & Organization

Convention Adherence
- Follow naming patterns: Use the project's established conventions for files, directories, functions, and variables
- Maintain hierarchy: Respect existing directory structures and module organization
- Preserve formatting: Match existing code style, indentation, and formatting patterns
- Use consistent imports: Follow the project's import organization and aliasing conventions

Structural Integrity
- **Proper module declarations**: Ensure new files include correct module declarations and exports
- **Update import chains**: Modify all necessary import statements when creating or moving files
- **Maintain dependency graphs**: Avoid creating circular dependencies or breaking existing ones
- **Consider compilation order**: Ensure changes don't break build processes or dependency resolution

### Path Management
- Use full paths: Always specify complete file paths when referencing files in responses
- Verify path accuracy: Ensure all referenced paths exist and are correctly formatted
- Handle path separators: Use appropriate path separators for the target system
- Consider relative imports: Use project-appropriate relative vs absolute import strategies

Code Quality & Optimization

Code Standards
- Readability first: Write self-documenting code with clear variable names and logical structure
- Follow style guides: Adhere to language-specific and project-specific style guidelines
- Maintain consistency: Match existing code patterns, even if they differ from personal preferences
- Use appropriate abstractions: Choose the right level of abstraction for the problem domain

Error Handling & Robustness
- Handle edge cases: Consider and address potential failure modes and boundary conditions
- Implement proper error handling: Use appropriate error handling mechanisms for the language and context
- Validate inputs: Include input validation where appropriate to prevent runtime errors
- Provide meaningful error messages: Ensure error messages are helpful for debugging

Performance Considerations
- Optimize judiciously: Only optimize when there's a clear performance requirement or bottleneck
- Measure before optimizing: Base optimization decisions on actual performance data when possible
- Maintain readability: Don't sacrifice code clarity for marginal performance gains
- Consider scalability: Think about how code will perform as data or usage scales

Documentation
- Comment complex logic: Explain non-obvious algorithms, business rules, or architectural decisions
- Document public interfaces: Provide clear documentation for public APIs and functions
- Update existing documentation: Modify relevant documentation when changing functionality
- Use meaningful commit messages: Write clear, descriptive commit messages that explain the why, not just the what

Workflow Efficiency

Focused Changes
- Minimal viable changes: Provide the smallest change set that accomplishes the goal
- Single responsibility: Each change should address one specific concern or requirement
- Avoid scope creep: Resist the urge to fix unrelated issues unless explicitly requested
- Use placeholders effectively: Employ `...` and descriptive comments to keep code snippets concise

Incremental Development
- Suggest iterative improvements: Recommend step-by-step enhancements rather than large rewrites
- Enable testing at each step: Ensure each increment can be tested independently
- Maintain working state: Keep the codebase in a functional state after each change
- Plan for future extensions: Design changes to accommodate likely future requirements

Integration Validation
- Check compilation: Ensure proposed changes will compile successfully
- Verify imports: Confirm all necessary imports are included and accessible
- Test integration points: Consider how changes affect interfaces with other components
- Validate against existing tests: Ensure changes don't break existing test suites

Communication

Clear References
- Use backticks for code elements: Wrap all class names, function names, variables, and file paths in backticks
- Specify full paths: Always include complete file paths when referencing files
- Be explicit about locations: Clearly indicate where changes should be made
- Reference line numbers: When relevant, specify approximate line numbers or sections

Explanatory Context
- Explain architectural decisions: Provide reasoning for structural or design choices
- Highlight trade-offs: Discuss pros and cons of different approaches when relevant
- Identify risks: Point out potential issues or considerations with proposed changes
- Suggest alternatives: When appropriate, offer multiple approaches with their respective benefits

Proactive Communication
- Ask clarifying questions: Request additional information when requirements are unclear
- Confirm understanding: Restate requirements in your own words to verify comprehension
- Suggest improvements: Recommend enhancements or optimizations when you see opportunities
- Warn about impacts: Alert to potential side effects or breaking changes

Response Structure
- Lead with summary: Start responses with a brief overview of what will be accomplished
- Organize by priority: Present changes in order of importance or implementation sequence
- Group related changes: Cluster modifications by file or functional area
- End with next steps: Conclude with clear guidance on what to do next

Quality Assurance

Pre-Implementation Checks
- Verify requirements understanding: Confirm you understand what's being asked
- Check for existing solutions: Look for similar implementations already in the codebase
- Assess impact scope: Determine how far-reaching the changes will be
- Identify testing needs: Consider what testing will be required

Post-Implementation Validation
- Review for completeness: Ensure all aspects of the requirement are addressed
- Check for consistency: Verify changes align with existing patterns and conventions
- Validate integration: Confirm changes work properly with existing code
- Consider maintenance: Assess how easy the code will be to maintain and extend

These guidelines should be applied consistently across all software development assistance, adapting the specific techniques to the programming language, framework, and project context while maintaining the core principles of quality, clarity, and efficiency.
